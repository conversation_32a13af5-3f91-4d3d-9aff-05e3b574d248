<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
use Hyperf\AsyncQueue\Driver\RedisDriver;

return [
    'default' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default',
        ],
        'channel' => '{queue}',
        'timeout' => 2,
        'retry_seconds' => 5,
        'handle_timeout' => 10,
        'processes' => 1,
        'concurrent' => [
            'limit' => 10,
        ],
        'max_messages' => 0,
    ],
    /**
     * 异步任务处理消费
     */
    'async-func-executor' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default',
        ],
        'channel' => '{async_queue}',
        'timeout' => 5,
        'retry_seconds' => 5,
        'handle_timeout' => 10, 
        'processes' => 5,
        'concurrent' => [
            'limit' => 500, 
        ],
        'max_messages' => 1000,
    ],
    'kline-aggregator' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default',
        ],
        'channel' => '{kline-aggregator}',
        'timeout' => 5,
        'retry_seconds' => 5,
        'handle_timeout' => 10,
        'processes' => 1,
        'concurrent' => [
            'limit' => 500,
        ],
        'max_messages' => 1000,
    ],
    /*撮合引擎订单相关异步处理消费进程*/
    'match-order' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default',
        ],
        'channel' => '{match-order}',
        'timeout' => 5,
        'retry_seconds' => 5,
        'handle_timeout' => 5,
        'processes' => 1,
        'concurrent' => [
            'limit' => 500,
        ],
        'max_messages' => 1000,
    ],

    // 强平队列配置
    'margin_liquidation' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default'
        ],
        'channel' => 'margin_liquidation',
        'timeout' => 2,
        'retry_seconds' => [1, 5, 10, 20], // 重试间隔
        'handle_timeout' => 30, // 30秒超时
        'processes' => 2, // 2个消费进程
        'max_messages' => 0,
        'concurrent' => [
            'limit' => 50, // 最大并发数50
        ],
    ],

    // 仓位监控队列配置
    'position_monitoring' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default'
        ],
        'channel' => 'position_monitoring',
        'timeout' => 2,
        'retry_seconds' => [1, 5, 10],
        'handle_timeout' => 30,
        'processes' => 1, // 单进程处理，避免并发问题
        'max_messages' => 0,
        'concurrent' => [
            'limit' => 10,
        ],
    ],
    'socket-message' => [
        'driver' => RedisDriver::class,
        'redis' => [
            'pool' => 'default'
        ],
        'channel' => 'socket-message',
        'timeout' => 2,
        'retry_seconds' => [1, 2, 3],
        'handle_timeout' => 5,
        'processes' => 1,
        'max_messages' => 100000,
        'concurrent' => [
            'limit' => 500,
        ],
    ]
];
