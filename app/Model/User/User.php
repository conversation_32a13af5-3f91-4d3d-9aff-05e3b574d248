<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易所用户模型
 */

namespace App\Model\User;

use App\Model\User\Enums\RegisterType;
use App\Model\User\Enums\Status;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\HasOne;
use Hyperf\Database\Model\SoftDeletes;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Filesystem\FilesystemFactory;

/**
 * @property int $id 用户ID，主键
 * @property int|null $parent_id 上级ID
 * @property string $account 用户account
 * @property string $username 用户名
 * @property string $display_name 展示名称，默认为用户名
 * @property string|null $email 邮箱
 * @property string|null $phone 手机号
 * @property string|null $phone_country_code 手机国家代码
 * @property string $password 登录密码
 * @property string|null $fund_password 资金密码
 * @property string|null $avatar 头像URL
 * @property string $invite_code 邀请码
 * @property RegisterType $register_type 注册类型:1=邮箱,2=手机,3=苹果,4=谷歌
 * @property Status $status 状态:1=正常,2=禁用,3=注销
 * @property Carbon|null $last_login_at 最后登录时间
 * @property string|null $last_login_ip 最后登录IP
 * @property string|null $last_login_device 最后登录设备
 * @property array|null $third_party_auth 第三方登录认证信息(Apple/Google等)
 * @property array|null $social_bindings 社交媒体绑定信息(Telegram/微信/Twitter等)
 * @property int|null $agent_id 代理商ID
 * @property int|null $agent_client_id 代理商直客ID
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property Carbon|null $deleted_at 删除时间
 */
final class User extends Model
{
    use SoftDeletes;

    #[Inject]
    protected FilesystemFactory $filesystemFactory;

    /**
     * 用户ID，主键
     */
    public const FIELD_ID = 'id';
    /**
     * 上级ID
     */
    public const FIELD_PARENT_ID = 'parent_id';
    /**
     * 用户account
     */
    public const FIELD_ACCOUNT = 'account';
    /**
     * 用户名
     */
    public const FIELD_USERNAME = 'username';
    /**
     * 展示名称，默认为用户名
     */
    public const FIELD_DISPLAY_NAME = 'display_name';
    /**
     * 邮箱
     */
    public const FIELD_EMAIL = 'email';
    /**
     * 手机号
     */
    public const FIELD_PHONE = 'phone';
    /**
     * 手机国家代码
     */
    public const FIELD_PHONE_COUNTRY_CODE = 'phone_country_code';
    /**
     * 登录密码
     */
    public const FIELD_PASSWORD = 'password';
    /**
     * 资金密码
     */
    public const FIELD_FUND_PASSWORD = 'fund_password';
    /**
     * 头像URL
     */
    public const FIELD_AVATAR = 'avatar';
    /**
     * 邀请码
     */
    public const FIELD_INVITE_CODE = 'invite_code';
    /**
     * 注册类型:1=邮箱,2=手机,3=苹果,4=谷歌
     */
    public const FIELD_REGISTER_TYPE = 'register_type';
    /**
     * 状态:1=正常,2=禁用,3=注销
     */
    public const FIELD_STATUS = 'status';
    /**
     * 最后登录时间
     */
    public const FIELD_LAST_LOGIN_AT = 'last_login_at';
    /**
     * 最后登录IP
     */
    public const FIELD_LAST_LOGIN_IP = 'last_login_ip';
    /**
     * 最后登录设备
     */
    public const FIELD_LAST_LOGIN_DEVICE = 'last_login_device';
    /**
     * 第三方登录认证信息
     */
    public const FIELD_THIRD_PARTY_AUTH = 'third_party_auth';
    /**
     * 社交媒体绑定信息
     */
    public const FIELD_SOCIAL_BINDINGS = 'social_bindings';
    /**
     * 代理商ID
     */
    public const FIELD_AGENT_ID = 'agent_id';
    /**
     * 代理商直客ID
     */
    public const FIELD_AGENT_CLIENT_ID = 'agent_client_id';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * 删除时间
     */
    public const FIELD_DELETED_AT = 'deleted_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user';

    protected array $appends = [
        'avatar_url',
    ];

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 用户ID，主键
        'parent_id', // 上级ID
        'account', // 用户account
        'username', // 用户名
        'display_name', // 展示名称，默认为用户名
        'email', // 邮箱
        'phone', // 手机号
        'phone_country_code', // 手机国家代码
        'password', // 登录密码
        'fund_password', // 资金密码
        'avatar', // 头像URL
        'invite_code', // 邀请码
        'register_type', // 注册类型
        'status', // 状态
        'last_login_at', // 最后登录时间
        'last_login_ip', // 最后登录IP
        'last_login_device', // 最后登录设备
        'third_party_auth', // 第三方登录认证信息
        'social_bindings', // 社交媒体绑定信息
        'agent_id', // 代理商ID
        'agent_client_id', // 代理商直客ID
        'created_at', // 创建时间
        'updated_at', // 更新时间
        'deleted_at', // 删除时间
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected array $hidden = [
        'password',
        'fund_password',
        'google2fa_secret',
        'third_party_auth'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 用户ID，主键
        'parent_id' => 'integer', // 上级ID
        'register_type' => RegisterType::class, // 注册类型
        'status' => Status::class, // 状态
        'third_party_auth' => 'array', // 第三方登录认证信息
        'social_bindings' => 'array', // 社交媒体绑定信息
        'agent_id' => 'integer', // 代理商ID
        'agent_client_id' => 'integer', // 代理商直客ID
        'last_login_at' => 'datetime', // 最后登录时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
        'deleted_at' => 'datetime', // 删除时间
    ];

    public function setPasswordAttribute($value): void
    {
        if ($value) {
            $this->attributes['password'] = password_hash($value, PASSWORD_DEFAULT);
        }
    }

    public function setFundPasswordAttribute($value): void
    {
        if ($value) {
            $this->attributes['fund_password'] = password_hash($value, PASSWORD_DEFAULT);
        }
    }

    /**
     * 处理 avatar url
     */
    public function getAvatarUrlAttribute(): string
    {
        if (empty($this->avatar) || str_starts_with($this->avatar, 'http')) {
            return $this->avatar ?? '';
        }
        $filesystem = $this->filesystemFactory->get(config('file.default', 'local'));
        return $filesystem->publicUrl($this->avatar);
    }

    /**
     * 关联 UserKycVerification
     */
    public function kycVerification(): HasOne
    {
        return $this->hasOne(UserKycVerification::class, 'user_id', 'id');
    }

    /**
     * 用户当前等级，关联 cpx_user_vip_level
     */
    public function currentVipLevel(): HasOne
    {
        return $this->hasOne(UserVipLevel::class, 'user_id', 'id')->where(UserVipLevel::FIELD_IS_ACTIVE, 1);
    }

    /**
     * 获取用户ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置用户ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取上级ID
     */
    public function getParentId(): ?int
    {
        return $this->parent_id;
    }

    /**
     * 设置上级ID
     */
    public function setParentId(?int $value): static
    {
        $this->parent_id = $value;
        return $this;
    }

    /**
     * 获取用户account
     */
    public function getAccount(): string
    {
        return $this->account;
    }

    /**
     * 设置用户account
     */
    public function setAccount(string $value): static
    {
        $this->account = $value;
        return $this;
    }

    /**
     * 获取用户名
     */
    public function getUsername(): string
    {
        return $this->username;
    }

    /**
     * 设置用户名
     */
    public function setUsername(string $value): static
    {
        $this->username = $value;
        return $this;
    }

    /**
     * 获取展示名称
     */
    public function getDisplayName(): string
    {
        return $this->display_name;
    }

    /**
     * 设置展示名称
     */
    public function setDisplayName(string $value): static
    {
        $this->display_name = $value;
        return $this;
    }

    /**
     * 获取邮箱
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * 设置邮箱
     */
    public function setEmail(?string $value): static
    {
        $this->email = $value;
        return $this;
    }

    /**
     * 获取手机号
     */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    /**
     * 设置手机号
     */
    public function setPhone(?string $value): static
    {
        $this->phone = $value;
        return $this;
    }

    /**
     * 获取手机国家代码
     */
    public function getPhoneCountryCode(): ?string
    {
        return $this->phone_country_code;
    }

    /**
     * 设置手机国家代码
     */
    public function setPhoneCountryCode(?string $value): static
    {
        $this->phone_country_code = $value;
        return $this;
    }

    /**
     * 获取登录密码
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    /**
     * 设置登录密码
     */
    public function setPassword(string $value): static
    {
        $this->password = $value;
        return $this;
    }

    /**
     * 获取资金密码
     */
    public function getFundPassword(): ?string
    {
        return $this->fund_password;
    }

    /**
     * 设置资金密码
     */
    public function setFundPassword(?string $value): static
    {
        $this->fund_password = $value;
        return $this;
    }

    /**
     * 获取头像URL
     */
    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    /**
     * 设置头像URL
     */
    public function setAvatar(?string $value): static
    {
        $this->avatar = $value;
        return $this;
    }

    /**
     * 获取邀请码
     */
    public function getInviteCode(): string
    {
        return $this->invite_code;
    }

    /**
     * 设置邀请码
     */
    public function setInviteCode(string $value): static
    {
        $this->invite_code = $value;
        return $this;
    }

    /**
     * 获取注册类型
     */
    public function getRegisterType(): RegisterType
    {
        return $this->register_type;
    }

    /**
     * 设置注册类型
     */
    public function setRegisterType(RegisterType $value): static
    {
        $this->register_type = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): Status
    {
        return $this->status;
    }

    /**
     * 设置状态
     */
    public function setStatus(Status $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取最后登录时间
     */
    public function getLastLoginAt(): ?Carbon
    {
        return $this->last_login_at;
    }

    /**
     * 设置最后登录时间
     */
    public function setLastLoginAt(?Carbon $value): static
    {
        $this->last_login_at = $value;
        return $this;
    }

    /**
     * 获取最后登录IP
     */
    public function getLastLoginIp(): ?string
    {
        return $this->last_login_ip;
    }

    /**
     * 设置最后登录IP
     */
    public function setLastLoginIp(?string $value): static
    {
        $this->last_login_ip = $value;
        return $this;
    }

    /**
     * 获取最后登录设备
     */
    public function getLastLoginDevice(): ?string
    {
        return $this->last_login_device;
    }

    /**
     * 设置最后登录设备
     */
    public function setLastLoginDevice(?string $value): static
    {
        $this->last_login_device = $value;
        return $this;
    }

    /**
     * 获取第三方登录认证信息
     */
    public function getThirdPartyAuth(): ?array
    {
        return $this->third_party_auth;
    }

    /**
     * 设置第三方登录认证信息
     */
    public function setThirdPartyAuth(?array $value): static
    {
        $this->third_party_auth = $value;
        return $this;
    }

    /**
     * 获取社交媒体绑定信息
     */
    public function getSocialBindings(): ?array
    {
        return $this->social_bindings;
    }

    /**
     * 设置社交媒体绑定信息
     */
    public function setSocialBindings(?array $value): static
    {
        $this->social_bindings = $value;
        return $this;
    }

    /**
     * 获取代理商ID
     */
    public function getAgentId(): ?int
    {
        return $this->agent_id;
    }

    /**
     * 设置代理商ID
     */
    public function setAgentId(?int $value): static
    {
        $this->agent_id = $value;
        return $this;
    }

    /**
     * 获取代理商直客ID
     */
    public function getAgentClientId(): ?int
    {
        return $this->agent_client_id;
    }

    /**
     * 设置代理商直客ID
     */
    public function setAgentClientId(?int $value): static
    {
        $this->agent_client_id = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取删除时间
     */
    public function getDeletedAt(): ?Carbon
    {
        return $this->deleted_at;
    }

    /**
     * 设置删除时间
     */
    public function setDeletedAt($value): static
    {
        $this->deleted_at = $value;
        return $this;
    }
}
