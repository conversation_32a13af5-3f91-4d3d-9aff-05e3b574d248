<?php

declare(strict_types=1);

namespace App\Model\Trade;

use App\Model\Currency\Currency;
use Hyperf\DbConnection\Model\Model;
use Hyperf\ModelCache\Cacheable;
use Hyperf\ModelCache\CacheableInterface;

/**
 * @property int $id 
 * @property int $currency_id 
 * @property int $market_type 
 * @property float $min_trade_num 限价最小下单数量
 * @property float $max_trade_num 限价最大下单数量
 * @property float $min_trade_price 最小下单价格
 * @property float $max_trade_price 最大下单价格
 * @property float $limit_price_rate 限价订单价格上下限百分比，也就是挂单不能超过最新价格的这个范围
 * @property float $maker_limit 市价单单笔最大开仓数量
 * @property float $limit_limit 限价单单笔最大开仓数量
 * @property int $order_limit 最大挂单数量（所有委托的订单最大挂单数量）
 * @property float $trigger_protect 价差保护百分比，指数和最新价格
 * @property float $liquidation_fee 强平费率
 * @property float $tick_size 最小价格变化
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property-read null|Currency $currency 
 */
class TradeConfig extends Model implements CacheableInterface
{
    /**
     * 强平费率
     */
    public const FIELD_LIQUIDATION_FEE = 'liquidation_fee';
    /**
     * 最小价格变化
     */
    public const FIELD_TICK_SIZE = 'tick_size';
    use Cacheable;
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 
     */
    public const FIELD_MARKET_TYPE = 'market_type';
    /**
     * 最小下单数量
     */
    public const FIELD_MIN_TRADE_NUM = 'min_trade_num';
    /**
     * 最大下单数量
     */
    public const FIELD_MAX_TRADE_NUM = 'max_trade_num';
    /**
     * 最小下单价格
     */
    public const FIELD_MIN_TRADE_PRICE = 'min_trade_price';
    /**
     * 最大下单数量
     */
    public const FIELD_MAX_TRADE_PRICE = 'max_trade_price';
    /**
     * 限价订单价格上下限百分比，也就是挂单不能超过最新价格的这个范围
     */
    public const FIELD_LIMIT_PRICE_RATE = 'limit_price_rate';
    /**
     * 市价单单笔最大开仓数量
     */
    public const FIELD_MAKER_LIMIT = 'maker_limit';
    /**
     * 限价单单笔最大开仓数量
     */
    public const FIELD_LIMIT_LIMIT = 'limit_limit';
    /**
     * 最大挂单数量
     */
    public const FIELD_ORDER_LIMIT = 'order_limit';
    /**
     * 价差保护百分比，指数和最新价格
     */
    public const FIELD_TRIGGER_PROTECT = 'trigger_protect';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_config';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'market_type', 'min_trade_num', 'max_trade_num', 'min_trade_price', 'max_trade_price', 'limit_price_rate', 'maker_limit', 'limit_limit', 'order_limit', 'trigger_protect', 'liquidation_fee', 'tick_size', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'currency_id' => 'integer',
        'market_type' => 'integer',
        // 限价最小下单数量
        'min_trade_num' => 'float',
        // 限价最大下单数量
        'max_trade_num' => 'float',
        // 最小下单价格
        'min_trade_price' => 'float',
        // 最大下单价格
        'max_trade_price' => 'float',
        // 限价订单价格上下限百分比，也就是挂单不能超过最新价格的这个范围
        'limit_price_rate' => 'float',
        // 市价单单笔最大开仓数量
        'maker_limit' => 'float',
        // 限价单单笔最大开仓数量
        'limit_limit' => 'float',
        // 最大挂单数量（所有委托的订单最大挂单数量）
        'order_limit' => 'integer',
        // 价差保护百分比，指数和最新价格
        'trigger_protect' => 'float',
        // 强平费率
        'liquidation_fee' => 'float',
        // 最小价格变化
        'tick_size' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMarketType() : int
    {
        return $this->market_type;
    }
    public function setMarketType($value) : object
    {
        $this->market_type = $value;
        return $this;
    }
    public function getMinTradeNum() : float
    {
        return $this->min_trade_num;
    }
    public function setMinTradeNum($value) : object
    {
        $this->min_trade_num = $value;
        return $this;
    }
    public function getMaxTradeNum() : float
    {
        return $this->max_trade_num;
    }
    public function setMaxTradeNum($value) : object
    {
        $this->max_trade_num = $value;
        return $this;
    }
    public function getMinTradePrice() : float
    {
        return $this->min_trade_price;
    }
    public function setMinTradePrice($value) : object
    {
        $this->min_trade_price = $value;
        return $this;
    }
    public function getMaxTradePrice() : float
    {
        return $this->max_trade_price;
    }
    public function setMaxTradePrice($value) : object
    {
        $this->max_trade_price = $value;
        return $this;
    }
    public function getLimitPriceRate() : int
    {
        return $this->limit_price_rate;
    }
    public function setLimitPriceRate($value) : object
    {
        $this->limit_price_rate = $value;
        return $this;
    }
    public function getMakerLimit() : float
    {
        return $this->maker_limit;
    }
    public function setMakerLimit($value) : object
    {
        $this->maker_limit = $value;
        return $this;
    }
    public function getLimitLimit() : float
    {
        return $this->limit_limit;
    }
    public function setLimitLimit($value) : object
    {
        $this->limit_limit = $value;
        return $this;
    }
    public function getOrderLimit() : int
    {
        return $this->order_limit;
    }
    public function setOrderLimit($value) : object
    {
        $this->order_limit = $value;
        return $this;
    }
    public function getTriggerProtect() : int
    {
        return $this->trigger_protect;
    }
    public function setTriggerProtect($value) : object
    {
        $this->trigger_protect = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }

    public function currency(): \Hyperf\Database\Model\Relations\HasOne
    {
        return $this->hasOne(Currency::class,'id','currency_id');
    }
    public function getLiquidationFee() : float
    {
        return $this->liquidation_fee;
    }
    public function setLiquidationFee($value) : object
    {
        $this->liquidation_fee = $value;
        return $this;
    }
    public function getTickSize() : float
    {
        return $this->tick_size;
    }
    public function setTickSize($value) : object
    {
        $this->tick_size = $value;
        return $this;
    }
}
