<?php

declare(strict_types=1);
/**
 * TipoffsRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Request\SysConfig;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class SysConfigRequest extends BaseFormRequest
{

    /**
     * 验证规则
     * Summary of configInfoRules
     * @return array{dynamics_id: string[]}
     */
    public function configInfoRules(): array{
        return [
            'code'=> ['required','string'],
        ]; 
    }


    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'code'=>'配置标识'
        ];
    }
}
