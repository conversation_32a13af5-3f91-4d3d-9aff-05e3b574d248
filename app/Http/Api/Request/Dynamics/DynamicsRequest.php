<?php

declare(strict_types=1);
/**
 * DynamicsRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Request\Dynamics;

use App\Http\Api\Request\BaseFormRequest;

class DynamicsRequest extends BaseFormRequest
{

    /**
     * 列表验证规则
     *
     * @return array
     */
    public function listRules(): array
    {
        return [
            'page' => ['required','integer'],
            'page_size' => ['required','integer'],
        ];
    }

    /**
     * 详情验证规则
     *
     * @return array
     */
    public function detailRules(): array
    {
        return [
            'dynamics_id' => ['required','integer','exists:cpx_dynamics,id']
        ];
    }

    /**
     * 发布动态验证规则
     *
     * @return array
     */
    public function createRules(): array
    {
        return [
            'content' => ['required','string']
        ];
    }

    /**
     * 评论验证规则
     *
     * @return array
     */
    public function commentRules(): array
    {
        return [
            'dynamics_id' => ['required','integer'],
            'content' => ['required','string']
        ];
    }

    /**
     * 评论列表验证规则
     *
     * @return array
     */
    public function commentListRules(): array
    {
        return [
            'dynamics_id' => ['required','integer'],
            'page' => ['required','integer'],
            'page_size' => ['required','integer'],
        ];
    }

    /**
     * 点赞验证规则
     *
     * @return array
     */
    public function likedRules(): array
    {
        return [
            'dynamics_id' => ['required','integer']
        ];
    }

    /**
     * 关注 验证规则
     *
     * @return array
     */
    public function concernRules(): array
    {
        return [
            'dynamics_id' => ['required','integer']
        ];
    }

    /**
     * 取消关注 验证规则
     *
     * @return array
     */
    public function unConcernRules(): array
    {
        return [
            'dynamics_id' => ['required','integer']
        ];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [];
    }
}
