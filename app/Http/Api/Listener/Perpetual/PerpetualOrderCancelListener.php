<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Http\Api\Event\Perpetual\OrderCancelEvent;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualPositionTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualMarginTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualLockTrait;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Match\MatchOrder;
use App\Model\WebsocketData\OrderData\Margin\PerpetualOrderCancelMessage;
use App\Job\Socket\MessageSendJob;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

#[Listener]
class PerpetualOrderCancelListener implements ListenerInterface
{
    use PerpetualPositionTrait, PerpetualMarginTrait, PerpetualLockTrait;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-trade', 'perpetual-logs');
    }

    public function listen(): array
    {
        return [
            OrderCancelEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderCancelEvent) {
            return;
        }

        $orderId = $event->order_id;

        $this->logger->info('收到永续合约订单取消事件', ['order_id' => $orderId]);

        try {
            $lockKey = $this->getOrderLockKey($orderId);
            $this->executeWithLock($lockKey, function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processPerpetualOrderCancel($orderId);
                });
            });

            $this->logger->info('永续合约订单取消处理完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单取消处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理永续合约订单取消
     */
    protected function processPerpetualOrderCancel(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找永续合约订单
        $perpetualOrder = TradePerpetualOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$perpetualOrder) {
            $this->logger->info('永续合约订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 处理订单取消后的资源释放
        $this->processOrderCancelCleanup($perpetualOrder, $matchOrder);

        // 推送订单取消消息
        $this->pushCancelMessage($perpetualOrder, $matchOrder);
    }

    /**
     * 处理订单取消后的资源释放
     */
    protected function processOrderCancelCleanup(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        // 1. 解冻剩余保证金
        $this->unfreezeRemainingMargin($order, $matchOrder);

        // 2. 解冻平仓订单的冻结持仓
        if ($this->isCloseOperation($order->side)) {
            $this->unfreezeRemainingPosition($order, $matchOrder);
        }

        $this->logger->info('订单取消清理完成', [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'side' => $order->side,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount
        ]);
    }

    /**
     * 解冻剩余保证金
     */
    protected function unfreezeRemainingMargin(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        // 计算剩余需要解冻的保证金
        $remainingFrozenMargin = bcsub((string)$order->frozen_amount, (string)$order->used_amount, 18);
        
        if (bccomp($remainingFrozenMargin, '0', 18) <= 0) {
            $this->logger->info('永续合约订单无剩余保证金需要解冻', [
                'order_id' => $order->id,
                'frozen_amount' => $order->frozen_amount,
                'used_amount' => $order->used_amount
            ]);
            return;
        }

        // 解冻剩余保证金
        $result = $this->unfreezeAllMargin(
            $order->user_id,
            $order->currency_id,
            (float)$remainingFrozenMargin
        );

        if (!$result) {
            throw new \RuntimeException('解冻剩余保证金失败');
        }

        $this->logger->info('永续合约订单取消，解冻剩余保证金成功', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'currency_id' => $order->currency_id,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount,
            'unfrozen_amount' => (float)$remainingFrozenMargin
        ]);
    }

    /**
     * 解冻剩余持仓
     */
    protected function unfreezeRemainingPosition(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        // 计算剩余需要解冻的持仓数量
        $remainingQuantity = bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity, 18);
        
        if (bccomp($remainingQuantity, '0', 18) <= 0) {
            $this->logger->info('平仓订单无剩余持仓需要解冻', [
                'order_id' => $order->id,
                'total_quantity' => $matchOrder->quantity,
                'filled_quantity' => $matchOrder->fill_quantity
            ]);
            return;
        }

        $positionSide = $this->getPositionSideFromContractSide($order->side);
        
        // 解冻剩余持仓
        $this->unfreezePositionQuantity(
            $order->user_id,
            $order->currency_id,
            $order->margin_mode,
            $positionSide,
            (float)$remainingQuantity
        );

        $this->logger->info('平仓订单取消，解冻剩余持仓成功', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'currency_id' => $order->currency_id,
            'margin_mode' => $order->margin_mode,
            'position_side' => $positionSide,
            'unfrozen_quantity' => (float)$remainingQuantity
        ]);
    }

    /**
     * 推送订单取消消息到WebSocket
     */
    protected function pushCancelMessage(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        try {
            // 计算剩余数量
            $remainingQuantity = bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity, 18);

            // 构造推送数据
            $messageData = [
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'order_type' => $matchOrder->order_type,
                'price' => $order->price,
                'quantity' => $matchOrder->quantity,
                'filled_quantity' => $matchOrder->fill_quantity,
                'remaining_quantity' => (float)$remainingQuantity,
                'cancel_time' => time(),
            ];

            // 创建消息对象
            $message = new PerpetualOrderCancelMessage();

            // 推送到异步任务
            pushAsyncJob('socket-message', new MessageSendJob($order->user_id, $messageData, $message));

            $this->logger->info('永续合约订单取消消息推送成功', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'remaining_quantity' => (float)$remainingQuantity
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单取消消息推送失败', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


}
