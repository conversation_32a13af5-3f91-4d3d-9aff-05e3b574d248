<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Http\Api\Event\Perpetual\OrderFilledEvent;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualPositionTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualMarginTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualLockTrait;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Match\MatchOrder;
use App\Model\WebsocketData\OrderData\Margin\PerpetualOrderFilledMessage;
use App\Job\Socket\MessageSendJob;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

#[Listener]
class PerpetualOrderFilledListener implements ListenerInterface
{
    use PerpetualPositionTrait, PerpetualMarginTrait, PerpetualLockTrait;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-trade', 'perpetual-logs');
    }

    public function listen(): array
    {
        return [
            OrderFilledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderFilledEvent) {
            return;
        }

        $orderId = $event->order_id;

        $this->logger->info('收到永续合约订单完全成交事件', ['order_id' => $orderId]);

        try {
            $lockKey = $this->getOrderLockKey($orderId);
            $this->executeWithLock($lockKey, function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processPerpetualOrderFilled($orderId);
                });
            });

            $this->logger->info('永续合约订单完全成交处理完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单完全成交处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理永续合约订单完全成交
     */
    protected function processPerpetualOrderFilled(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找永续合约订单
        $perpetualOrder = TradePerpetualOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$perpetualOrder) {
            $this->logger->info('永续合约订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 处理完全成交后的清理工作
        $this->processOrderFilledCleanup($perpetualOrder);

        // 推送订单完全成交消息
        $this->pushFilledMessage($perpetualOrder, $matchOrder);
    }

    /**
     * 处理订单完全成交后的清理工作
     */
    protected function processOrderFilledCleanup(TradePerpetualOrder $order): void
    {
        // 1. 返还多余的冻结保证金（主要针对市价单的2%缓冲）
        $this->returnExcessFrozenMargin($order);

        // 2. 解冻平仓订单的冻结持仓
        if ($this->isCloseOperation($order->side)) {
            $this->unfreezeCloseOrderPosition($order);
        }

        $this->logger->info('订单完全成交清理完成', [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'side' => $order->side,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount
        ]);
    }

    /**
     * 返还多余的冻结保证金
     */
    protected function returnExcessFrozenMargin(TradePerpetualOrder $order): void
    {
        // 计算多余保证金
        $excessMargin = bcsub((string)$order->frozen_amount, (string)$order->used_amount, 18);
        
        if (bccomp($excessMargin, '0', 18) <= 0) {
            $this->logger->info('永续合约订单无多余保证金需要返还', [
                'order_id' => $order->id,
                'frozen_amount' => $order->frozen_amount,
                'used_amount' => $order->used_amount
            ]);
            return;
        }

        // 返还多余保证金
        $result = $this->returnExcessMargin(
            $order->user_id,
            $order->currency_id,
            (float)$excessMargin
        );

        if (!$result) {
            throw new \RuntimeException('返还多余保证金失败');
        }

        $this->logger->info('永续合约订单完全成交，返还多余保证金成功', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'currency_id' => $order->currency_id,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount,
            'returned_amount' => (float)$excessMargin
        ]);
    }

    /**
     * 解冻平仓订单的冻结持仓
     */
    protected function unfreezeCloseOrderPosition(TradePerpetualOrder $order): void
    {
        // 平仓订单完全成交后，需要解冻剩余的冻结持仓（如果有的话）
        // 这里主要处理部分成交后取消的情况，正常完全成交不会有剩余冻结
        
        $positionSide = $this->getPositionSideFromContractSide($order->side);
        
        // 获取撮合引擎订单信息
        $matchOrder = MatchOrder::query()
            ->where('id', $order->match_order_id)
            ->first();
            
        if (!$matchOrder) {
            return;
        }
        
        // 计算未成交数量（理论上应该为0，但为了安全起见）
        $unfilledQuantity = bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity, 18);
        
        if (bccomp($unfilledQuantity, '0', 18) > 0) {
            $this->unfreezePositionQuantity(
                $order->user_id,
                $order->currency_id,
                $order->margin_mode,
                $positionSide,
                (float)$unfilledQuantity
            );
            
            $this->logger->info('解冻平仓订单剩余持仓', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'unfrozen_quantity' => (float)$unfilledQuantity
            ]);
        }
    }

    /**
     * 推送订单完全成交消息到WebSocket
     */
    protected function pushFilledMessage(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        try {
            // 获取仓位信息
            $position = null;
            if ($order->position_id) {
                $position = \App\Model\Trade\TradePerpetualPosition::find($order->position_id);
            }

            // 构造推送数据
            $messageData = [
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'position_id' => $order->position_id,
                'quantity' => $matchOrder->quantity,
                'filled_quantity' => $matchOrder->fill_quantity,
                'avg_price' => $matchOrder->avg_price,
                'filled_time' => time(),
            ];

            // 添加仓位信息（特别是平仓后仓位为0的情况）
            if ($position) {
                $messageData['position_quantity'] = $position->quantity;
                $messageData['position_entry_price'] = $position->entry_price;
                $messageData['position_margin'] = $position->margin_amount;
            } else {
                // 仓位已完全平仓
                $messageData['position_quantity'] = 0;
                $messageData['position_entry_price'] = 0;
                $messageData['position_margin'] = 0;
            }

            // 创建消息对象
            $message = new PerpetualOrderFilledMessage();

            // 推送到异步任务
            pushAsyncJob('socket-message', new MessageSendJob($order->user_id, $messageData, $message));

            $this->logger->info('永续合约订单完全成交消息推送成功', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'position_quantity' => $messageData['position_quantity']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单完全成交消息推送失败', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


}
