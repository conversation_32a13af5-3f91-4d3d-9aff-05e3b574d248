<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual\Traits;

use App\Model\Trade\TradePerpetualPosition;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Enums\Trade\Perpetual\ContractSide;

trait PerpetualPositionTrait
{
    /**
     * 查找或创建仓位
     */
    protected function findOrCreatePosition(int $userId, int $currencyId, int $marginMode, int $side): TradePerpetualPosition
    {
        $position = TradePerpetualPosition::query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $side)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if (!$position) {
            $position = new TradePerpetualPosition();
            $position->user_id = $userId;
            $position->currency_id = $currencyId;
            $position->margin_mode = $marginMode;
            $position->side = $side;
            $position->quantity = 0;
            $position->available_quantity = 0;
            $position->frozen_quantity = 0;
            $position->entry_price = 0;
            $position->margin_amount = 0;
            $position->initial_margin = 0;
            $position->maintenance_margin = 0;
            $position->realized_pnl = 0;
            $position->leverage = 1;
            $position->auto_add_margin = 0;
            $position->status = PositionStatus::HOLDING->value;
            $position->save();
        }

        return $position;
    }

    /**
     * 更新开仓仓位
     */
    protected function updateOpenPosition(TradePerpetualPosition $position, float $filledQuantity, float $filledPrice, float $usedMargin, float $leverage): void
    {
        // 计算新的持仓数量
        $newQuantity = bcadd((string)$position->quantity, (string)$filledQuantity, 18);
        
        // 计算新的持仓均价
        $currentValue = bcmul((string)$position->quantity, (string)$position->entry_price, 18);
        $newValue = bcmul((string)$filledQuantity, (string)$filledPrice, 18);
        $totalValue = bcadd($currentValue, $newValue, 18);
        
        $newEntryPrice = bccomp($newQuantity, '0', 18) > 0 
            ? bcdiv($totalValue, $newQuantity, 18) 
            : '0';

        // 更新仓位数据
        $position->quantity = (float)$newQuantity;
        $position->available_quantity = (float)$newQuantity;
        $position->entry_price = (float)$newEntryPrice;
        $position->margin_amount = (float)bcadd((string)$position->margin_amount, (string)$usedMargin, 18);
        $position->leverage = $leverage;
        
        // 计算初始保证金和维持保证金
        $notionalValue = bcmul($newQuantity, $newEntryPrice, 18);
        $position->initial_margin = (float)bcdiv($notionalValue, (string)$leverage, 18);
        $position->maintenance_margin = (float)bcmul($notionalValue, '0.005', 18); // 0.5%维持保证金率
        
        $position->save();
    }

    /**
     * 更新平仓仓位
     */
    protected function updateClosePosition(TradePerpetualPosition $position, float $filledQuantity, float $filledPrice): float
    {
        // 计算已实现盈亏
        $pnlPerUnit = bcsub((string)$filledPrice, (string)$position->entry_price, 18);
        
        // 根据仓位方向调整盈亏计算
        if ($position->side == 2) { // 空头仓位
            $pnlPerUnit = bcsub((string)$position->entry_price, (string)$filledPrice, 18);
        }
        
        $realizedPnl = bcmul($pnlPerUnit, (string)$filledQuantity, 18);
        
        // 计算释放的保证金比例（平仓数量 / 原有持仓数量）
        $marginRatio = bccomp((string)$position->quantity, '0', 18) > 0
            ? bcdiv((string)$filledQuantity, (string)$position->quantity, 18)
            : '0';
        
        $releasedMargin = bcmul((string)$position->margin_amount, $marginRatio, 18);
        
        // 更新仓位数据
        $position->quantity = (float)bcsub((string)$position->quantity, (string)$filledQuantity, 18);
        $position->available_quantity = (float)bcsub((string)$position->available_quantity, (string)$filledQuantity, 18);
        $position->realized_pnl = (float)bcadd((string)$position->realized_pnl, $realizedPnl, 18);
        $position->margin_amount = (float)bcsub((string)$position->margin_amount, $releasedMargin, 18);
        
        // 重新计算保证金
        if (bccomp((string)$position->quantity, '0', 18) > 0) {
            $notionalValue = bcmul((string)$position->quantity, (string)$position->entry_price, 18);
            $position->initial_margin = (float)bcdiv($notionalValue, (string)$position->leverage, 18);
            $position->maintenance_margin = (float)bcmul($notionalValue, '0.005', 18);
        } else {
            // 仓位完全平仓
            $position->initial_margin = 0;
            $position->maintenance_margin = 0;
            $position->status = PositionStatus::CLOSED->value;
        }
        
        $position->save();
        
        return (float)$releasedMargin;
    }

    /**
     * 解冻仓位数量
     */
    protected function unfreezePositionQuantity(int $userId, int $currencyId, int $marginMode, int $side, float $quantity): void
    {
        $position = TradePerpetualPosition::query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $side)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if ($position) {
            $position->available_quantity = (float)bcadd((string)$position->available_quantity, (string)$quantity, 18);
            $position->frozen_quantity = (float)bcsub((string)$position->frozen_quantity, (string)$quantity, 18);
            $position->save();
        }
    }

    /**
     * 根据合约方向获取仓位方向
     */
    protected function getPositionSideFromContractSide(int $contractSide): int
    {
        return match($contractSide) {
            ContractSide::BUY_OPEN->value, ContractSide::SELL_CLOSE->value => 1, // 多头
            ContractSide::SELL_OPEN->value, ContractSide::BUY_CLOSE->value => 2, // 空头
            default => 1
        };
    }

    /**
     * 判断是否为开仓操作
     */
    protected function isOpenOperation(int $contractSide): bool
    {
        return in_array($contractSide, [
            ContractSide::BUY_OPEN->value,
            ContractSide::SELL_OPEN->value
        ]);
    }

    /**
     * 判断是否为平仓操作
     */
    protected function isCloseOperation(int $contractSide): bool
    {
        return in_array($contractSide, [
            ContractSide::BUY_CLOSE->value,
            ContractSide::SELL_CLOSE->value
        ]);
    }
}
