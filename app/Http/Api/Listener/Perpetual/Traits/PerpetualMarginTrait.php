<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual\Traits;

use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\User\UserVipLevel;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Enum\CurrencyConfigKey;
use Hyperf\Context\ApplicationContext;
use Hyperf\Redis\Redis;

trait PerpetualMarginTrait
{
    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $currencyId): int
    {
        $redis = ApplicationContext::getContainer()->get(Redis::class);
        $key = CurrencyConfigKey::getCurrencyKey($currencyId);
        $quoteAssetsId = $redis->hGet($key, 'quote_assets_id');
        
        if ($quoteAssetsId === false || $quoteAssetsId === null) {
            throw new \RuntimeException("无法获取币种 {$currencyId} 的计价币种ID");
        }
        
        return (int)$quoteAssetsId;
    }

    /**
     * 退还多余保证金
     */
    protected function returnExcessMargin(int $userId, int $currencyId, float $excessAmount): bool
    {
        if (bccomp((string)$excessAmount, '0', 18) <= 0) {
            return true;
        }

        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
        
        $userAccountsAssetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
        
        return $userAccountsAssetService->unfreezeAsset(
            $userId,
            AccountType::FUTURES->value,
            $quoteCurrencyId,
            $excessAmount,
            FlowsType::PERPETUAL_MARGIN_UNFREEZE->value,
            0,
            'frozen',
            'available'
        );
    }

    /**
     * 释放平仓保证金
     */
    protected function releaseCloseMargin(int $userId, int $currencyId, float $marginAmount, float $realizedPnl): bool
    {
        if (bccomp((string)$marginAmount, '0', 18) <= 0) {
            return true;
        }

        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
        $userAccountsAssetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
        
        // 释放保证金到可用余额
        $result = $userAccountsAssetService->unfreezeAsset(
            $userId,
            AccountType::FUTURES->value,
            $quoteCurrencyId,
            $marginAmount,
            FlowsType::PERPETUAL_MARGIN_UNFREEZE->value,
            0,
            'frozen',
            'available'
        );

        if (!$result) {
            return false;
        }

        // 如果有已实现盈亏，需要单独处理
        if (bccomp((string)$realizedPnl, '0', 18) != 0) {
            $flowType = FlowsType::PERPETUAL_TRADE->value;

            // 盈亏直接影响可用余额
            if (bccomp((string)$realizedPnl, '0', 18) > 0) {
                // 盈利：增加可用余额
                $userAccountsAssetService->addAvailableAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $quoteCurrencyId,
                    abs($realizedPnl),
                    $flowType,
                    0,
                    'available'
                );
            } else {
                // 亏损：减少可用余额
                $userAccountsAssetService->deductAvailableAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $quoteCurrencyId,
                    abs($realizedPnl),
                    $flowType,
                    0,
                    'available'
                );
            }
        }

        return true;
    }

    /**
     * 计算实际使用的保证金
     */
    protected function calculateUsedMargin(float $quantity, float $price, float $leverage): float
    {
        $notionalValue = bcmul((string)$quantity, (string)$price, 18);
        return (float)bcdiv($notionalValue, (string)$leverage, 18);
    }

    /**
     * 获取用户永续合约手续费率
     */
    protected function getUserFuturesFeeRates(int $userId): array
    {
        $feeRates = UserVipLevel::getUserFeeRates($userId);

        if ($feeRates) {
            return [
                'maker_fee_rate' => $feeRates['futures_maker_fee_rate'],
                'taker_fee_rate' => $feeRates['futures_taker_fee_rate']
            ];
        }

        // 默认手续费率（如果用户没有VIP等级）
        return [
            'maker_fee_rate' => 0.0002, // 0.02% 挂单手续费
            'taker_fee_rate' => 0.0004  // 0.04% 吃单手续费
        ];
    }

    /**
     * 计算手续费
     */
    protected function calculateTradeFee(int $userId, float $quantity, float $price, int $orderType): float
    {
        $feeRates = $this->getUserFuturesFeeRates($userId);

        // 限价单使用maker费率，市价单使用taker费率
        $feeRate = $orderType == ContractOrderType::LIMIT->value
            ? $feeRates['maker_fee_rate']
            : $feeRates['taker_fee_rate'];

        $notionalValue = bcmul((string)$quantity, (string)$price, 18);
        return (float)bcmul($notionalValue, (string)$feeRate, 18);
    }

    /**
     * 扣除交易手续费（从冻结金额中扣除）
     */
    protected function deductTradeFee(int $userId, int $currencyId, float $feeAmount): bool
    {
        if (bccomp((string)$feeAmount, '0', 18) <= 0) {
            return true;
        }

        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
        $userAccountsAssetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);

        // 从冻结金额中扣除手续费
        return $userAccountsAssetService->deductFrozenAsset(
            $userId,
            AccountType::FUTURES->value,
            $quoteCurrencyId,
            $feeAmount,
            FlowsType::PERPETUAL_TRADE_FEE->value,
            0,
            'frozen'
        );
    }

    /**
     * 全额解冻保证金（用于订单取消）
     */
    protected function unfreezeAllMargin(int $userId, int $currencyId, float $frozenAmount): bool
    {
        if (bccomp((string)$frozenAmount, '0', 18) <= 0) {
            return true;
        }

        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
        $userAccountsAssetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
        
        return $userAccountsAssetService->unfreezeAsset(
            $userId,
            AccountType::FUTURES->value,
            $quoteCurrencyId,
            $frozenAmount,
            FlowsType::PERPETUAL_MARGIN_UNFREEZE->value,
            0,
            'frozen',
            'available'
        );
    }
}
