<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Http\Api\Event\Perpetual\OrderTradeEvent;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualPositionTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualMarginTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualLockTrait;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Match\MatchOrder;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\WebsocketData\OrderData\Margin\PerpetualOrderTradeMessage;
use App\Job\Socket\MessageSendJob;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

#[Listener]
class PerpetualOrderTradeListener implements ListenerInterface
{
    use PerpetualPositionTrait, PerpetualMarginTrait, PerpetualLockTrait;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-trade','perpetual-logs');
    }

    public function listen(): array
    {
        return [
            OrderTradeEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderTradeEvent) {
            return;
        }

        $tradeData = $event->order;

        $this->logger->info('收到永续合约订单成交事件', $tradeData);

        try {
            // 获取需要锁定的订单ID
            $lockKeys = [];
            if (isset($tradeData['buy_order_id'])) {
                $lockKeys[] = $this->getOrderLockKey($tradeData['buy_order_id']);
            }
            if (isset($tradeData['sell_order_id'])) {
                $lockKeys[] = $this->getOrderLockKey($tradeData['sell_order_id']);
            }

            // 使用Redis分布式锁
            $this->executeWithMultipleLocks($lockKeys, function () use ($tradeData) {
                Db::transaction(function () use ($tradeData) {
                    // 处理买方订单成交
                    if (isset($tradeData['buy_order_id'])) {
                        $this->processPerpetualTradeForOrder($tradeData, $tradeData['buy_order_id']);
                    }

                    // 处理卖方订单成交
                    if (isset($tradeData['sell_order_id'])) {
                        $this->processPerpetualTradeForOrder($tradeData, $tradeData['sell_order_id']);
                    }
                });
            });

            $this->logger->info('永续合约订单成交处理完成', [
                'trade_id' => $tradeData['trade_id'],
                'buy_order_id' => $tradeData['buy_order_id'] ?? null,
                'sell_order_id' => $tradeData['sell_order_id'] ?? null,
                'price' => $tradeData['price'],
                'quantity' => $tradeData['quantity']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单成交处理失败', [
                'trade_id' => $tradeData['trade_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理永续合约订单成交
     */
    protected function processPerpetualTradeForOrder(array $tradeData, int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找永续合约订单
        $perpetualOrder = TradePerpetualOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$perpetualOrder) {
            $this->logger->info('永续合约订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 提取成交数据
        $filledQuantity = (float)($tradeData['quantity'] ?? 0);
        $filledPrice = (float)($tradeData['price'] ?? 0);

        if ($filledQuantity <= 0 || $filledPrice <= 0) {
            $this->logger->warning('成交数据无效', [
                'order_id' => $orderId,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice
            ]);
            return;
        }

        // 处理成交
        $this->processOrderTrade($perpetualOrder, $matchOrder, $filledQuantity, $filledPrice);

        $this->logger->info('永续合约订单成交处理完成', [
            'perpetual_order_id' => $perpetualOrder->id,
            'match_order_id' => $matchOrder->id,
            'user_id' => $perpetualOrder->user_id,
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice
        ]);
    }

    /**
     * 处理订单成交逻辑
     */
    protected function processOrderTrade(TradePerpetualOrder $order, MatchOrder $matchOrder, float $filledQuantity, float $filledPrice): void
    {
        // 计算实际使用的保证金
        $usedMargin = $this->calculateUsedMargin($filledQuantity, $filledPrice, $order->leverage);

        // 计算并扣除手续费（从MatchOrder获取订单类型：1市价，2限价）
        $orderType = $matchOrder->order_type == 2 ? ContractOrderType::LIMIT->value : ContractOrderType::MARKET->value;
        $tradeFee = $this->calculateTradeFee($order->user_id, $filledQuantity, $filledPrice, $orderType);
        $this->deductTradeFee($order->user_id, $order->currency_id, $tradeFee);

        // 更新订单的已使用保证金和实际手续费
        $order->used_amount = (float)bcadd((string)$order->used_amount, (string)$usedMargin, 18);
        $order->actual_fee = (float)bcadd((string)$order->actual_fee, (string)$tradeFee, 18);
        $order->save();

        // 根据订单类型处理仓位
        if ($this->isOpenOperation($order->side)) {
            $this->processOpenTrade($order, $filledQuantity, $filledPrice, $usedMargin);
        } elseif ($this->isCloseOperation($order->side)) {
            $this->processCloseTrade($order, $filledQuantity, $filledPrice);
        }

        // 推送成交消息到WebSocket
        $this->pushTradeMessage($order, $matchOrder, $filledQuantity, $filledPrice, $tradeFee);
    }

    /**
     * 处理开仓成交
     */
    protected function processOpenTrade(TradePerpetualOrder $order, float $filledQuantity, float $filledPrice, float $usedMargin): void
    {
        $positionSide = $this->getPositionSideFromContractSide($order->side);

        // 查找或创建仓位
        $position = $this->findOrCreatePosition(
            $order->user_id,
            $order->currency_id,
            $order->margin_mode,
            $positionSide
        );

        // 更新仓位
        $this->updateOpenPosition($position, $filledQuantity, $filledPrice, $usedMargin, $order->leverage);

        // 关联订单与仓位（如果还未关联）
        if (!$order->position_id) {
            $order->position_id = $position->id;
            $order->save();
        }

        $this->logger->info('开仓成交处理完成', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'position_id' => $position->id,
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice,
            'used_margin' => $usedMargin,
            'actual_fee' => $order->actual_fee,
            'new_position_quantity' => $position->quantity,
            'new_entry_price' => $position->entry_price
        ]);
    }

    /**
     * 处理平仓成交
     */
    protected function processCloseTrade(TradePerpetualOrder $order, float $filledQuantity, float $filledPrice): void
    {
        $positionSide = $this->getPositionSideFromContractSide($order->side);

        // 查找仓位
        $position = $this->findOrCreatePosition(
            $order->user_id,
            $order->currency_id,
            $order->margin_mode,
            $positionSide
        );

        // 更新仓位并获取释放的保证金
        $releasedMargin = $this->updateClosePosition($position, $filledQuantity, $filledPrice);

        // 释放保证金和处理已实现盈亏
        $this->releaseCloseMargin($order->user_id, $order->currency_id, $releasedMargin, $position->realized_pnl);

        // 关联订单与仓位（如果还未关联）
        if (!$order->position_id) {
            $order->position_id = $position->id;
            $order->save();
        }

        $this->logger->info('平仓成交处理完成', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'position_id' => $position->id,
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice,
            'released_margin' => $releasedMargin,
            'realized_pnl' => $position->realized_pnl,
            'actual_fee' => $order->actual_fee,
            'remaining_quantity' => $position->quantity
        ]);
    }

    /**
     * 推送成交消息到WebSocket
     */
    protected function pushTradeMessage(TradePerpetualOrder $order, MatchOrder $matchOrder, float $filledQuantity, float $filledPrice, float $tradeFee): void
    {
        try {
            // 获取仓位信息
            $position = null;
            if ($order->position_id) {
                $position = $this->findPositionById($order->position_id);
            }

            // 构造推送数据
            $messageData = [
                'trade_id' => uniqid('trade_'),
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'position_id' => $order->position_id,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice,
                'trade_time' => time(),
            ];

            // 添加仓位信息
            if ($position) {
                $messageData['position_quantity'] = $position->quantity;
                $messageData['position_entry_price'] = $position->entry_price;
                $messageData['position_margin'] = $position->margin_amount;
            }

            // 创建消息对象
            $message = new PerpetualOrderTradeMessage();

            // 推送到异步任务
            pushAsyncJob('socket-message', new MessageSendJob($order->user_id, $messageData, $message));

            $this->logger->info('永续合约成交消息推送成功', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约成交消息推送失败', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 根据ID查找仓位
     */
    protected function findPositionById(int $positionId): ?\App\Model\Trade\TradePerpetualPosition
    {
        return \App\Model\Trade\TradePerpetualPosition::find($positionId);
    }


}
