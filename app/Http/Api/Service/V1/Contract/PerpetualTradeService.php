<?php

declare(strict_types=1);
/**
 * 永续合约交易服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Trade\TradePerpetualOrder;
use App\Model\Trade\TradePerpetualPosition;
use App\Model\Trade\TradeConfig;
use App\Model\Trade\TradeMarginLevel;
use App\Model\User\UserPerprtualConfig;
use App\Model\Match\MatchOrder;
use App\Enum\MarketType;
use App\Enum\CurrencyConfigKey;
use App\Enum\OrderStatus;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Service\MatchEngineOrderService;
use App\Model\Currency\Currency;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\Enums\Trade\Perpetual\MarginMode;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Enums\Trade\Perpetual\PerpetualOrderStatus;
use App\Enum\MarketData\TickerSyncKey;
use App\Model\Enums\User\FlowsType;
use App\Model\Enums\User\AccountType;
use App\Model\User\UserVipLevel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Redis\Redis;

class PerpetualTradeService
{
    #[Inject]
    protected TradePerpetualOrder $tradePerpetualOrder;

    #[Inject]
    protected TradePerpetualPosition $tradePerpetualPosition;

    #[Inject]
    protected TradeConfig $tradeConfig;

    #[Inject]
    protected TradeMarginLevel $tradeMarginLevel;

    #[Inject]
    protected UserPerprtualConfig $userPerpetualConfig;

    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected MatchEngineOrderService $matchEngineService;

    #[Inject]
    protected Currency $currency;

    #[Inject]
    protected \Hyperf\Snowflake\IdGeneratorInterface $idGenerator;

    #[Inject]
    protected \App\Model\Match\MatchOrder $matchOrder;

    #[Inject]
    protected \App\Model\Match\MatchTrade $matchTrade;

    #[Inject]
    protected \Psr\Log\LoggerInterface $logger;

    /**
     * 创建永续合约订单
     */
    public function createOrder(int $userId, array $orderData): array
    {
        return Db::transaction(function () use ($userId, $orderData) {
            // 提取订单参数
            $currencyId = (int)$orderData['currency_id'];
            $side = (int)$orderData['side'];
            $orderType = (int)$orderData['order_type'];
            $quantity = (float)$orderData['quantity'];
            $price = $orderType == ContractOrderType::LIMIT->value ? (float)$orderData['price'] : 0;
            $marginMode = (int)$orderData['margin_mode'];
            $leverage = (float)$orderData['leverage'];
            $reduceOnly = (bool)($orderData['reduce_only'] ?? false);
            $timeInForce = (int)($orderData['time_in_force'] ?? 1);

            // 1. 验证交易对配置
            $this->validateTradingPair($currencyId);

            // 2. 验证用户配置
            $this->validateUserConfig($userId, $marginMode);

            // 3. 获取交易配置
            $tradeConfig = $this->getTradeConfig($currencyId);

            // 4. 验证订单参数
            $this->validateOrderParams($orderData, $tradeConfig);

            // 5. 验证用户风险等级
            $this->checkUserRiskLevel($userId, $currencyId, $quantity, $price, $leverage);

            // 6. 根据订单类型处理
            if (in_array($side, [ContractSide::BUY_OPEN->value, ContractSide::SELL_OPEN->value])) {
                // 开仓订单
                return $this->handleOpenOrder($userId, $currencyId, $orderData, $tradeConfig);
            } else {
                // 平仓订单
                return $this->handleCloseOrder($userId, $currencyId, $orderData, $tradeConfig);
            }
        });
    }

    /**
     * 撤销永续合约订单
     */
    public function cancelOrder(int $userId, int $orderId): array
    {
        return Db::transaction(function () use ($userId, $orderId) {
            // 1. 查询订单和关联的撮合引擎订单
            $order = $this->tradePerpetualOrder->query()
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->first();

            if (!$order) {
                throw new BusinessException(ResultCode::FAIL, '订单不存在');
            }

            // 2. 查询撮合引擎订单状态
            $matchOrder = $this->matchOrder->query()
                ->where('id', $order->match_order_id)
                ->first();

            if (!$matchOrder) {
                throw new BusinessException(ResultCode::FAIL, '撮合引擎订单不存在');
            }

            // 3. 验证订单状态（只能撤销创建和pending的订单，部分成交的不能撤）
            if (!in_array($matchOrder->status, [OrderStatus::CREATED->value, OrderStatus::PENDING->value])) {
                throw new BusinessException(ResultCode::FAIL, '订单状态不允许撤销，当前状态：' . OrderStatus::getOrderStatusString($matchOrder->status));
            }

            // 4. 从撮合引擎撤销订单
            $symbol = $this->getCurrencySymbol($order->currency_id);
            $matchResult = $this->cancelOrderInMatchEngine($symbol, $matchOrder->order_id, $order->user_id);
            if (!$matchResult) {
                throw new BusinessException(ResultCode::FAIL, '撮合引擎撤单失败');
            }

            // 注意：保证金的解冻由监听器处理，这里不需要手动解冻

            return [
                'order_id' => $orderId,
                'status' => 'canceled',
                'canceled_at' => date('Y-m-d H:i:s')
            ];
        });
    }

    /**
     * 获取订单列表
     */
    public function getOrderList(int $userId, ?int $currencyId = null, ?int $marginMode = null, ?int $side = null, ?int $status = null, int $perPage = 20, int $page = 1): array
    {
        // 构建查询条件
        $query = $this->tradePerpetualOrder->query()
            ->select([
                'trade_perpetual_order.*',
                'match_orders.status as order_status',
                'match_orders.quantity as order_quantity',
                'match_orders.fill_quantity',
                'match_orders.avg_price',
                'match_orders.order_type as match_order_type',
                'currency.symbol'
            ])
            ->leftJoin('match_orders', 'trade_perpetual_order.match_order_id', '=', 'match_orders.id')
            ->leftJoin('currency', 'trade_perpetual_order.currency_id', '=', 'currency.id')
            ->where('trade_perpetual_order.user_id', $userId)
            ->orderBy('trade_perpetual_order.created_at', 'desc');

        // 添加筛选条件
        if ($currencyId) {
            $query->where('trade_perpetual_order.currency_id', $currencyId);
        }

        if ($marginMode) {
            $query->where('trade_perpetual_order.margin_mode', $marginMode);
        }

        if ($side) {
            $query->where('trade_perpetual_order.side', $side);
        }

        if ($status !== null) {
            $query->where('match_orders.status', $status);
        }

        // 分页查询
        $total = $query->count();
        $orders = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        // 格式化数据
        $data = $orders->map(function ($order) {
            return $this->formatOrderData($order);
        })->toArray();

        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail(int $userId, int $orderId): array
    {
        // 查询订单详情
        $order = $this->tradePerpetualOrder->query()
            ->select([
                'trade_perpetual_order.*',
                'match_orders.status as order_status',
                'match_orders.quantity as order_quantity',
                'match_orders.fill_quantity',
                'match_orders.avg_price',
                'match_orders.order_type as match_order_type',
                'match_orders.order_id as match_engine_order_id',
                'currency.symbol'
            ])
            ->leftJoin('match_orders', 'trade_perpetual_order.match_order_id', '=', 'match_orders.id')
            ->leftJoin('currency', 'trade_perpetual_order.currency_id', '=', 'currency.id')
            ->where('trade_perpetual_order.id', $orderId)
            ->where('trade_perpetual_order.user_id', $userId)
            ->first();

        if (!$order) {
            throw new BusinessException(ResultCode::FAIL, '订单不存在');
        }

        // 查询成交明细
        $trades = $this->getOrderTrades($order->match_engine_order_id, $userId);

        // 格式化订单数据
        $orderData = $this->formatOrderData($order);

        return [
            'order' => $orderData,
            'trades' => $trades
        ];
    }

    /**
     * 获取订单成交明细
     */
    private function getOrderTrades(string $matchEngineOrderId, int $userId): array
    {
        // 查询成交明细，需要查询买单和卖单两种情况
        $trades = $this->matchTrade->query()
            ->select([
                'match_trades.price',
                'match_trades.quantity',
                'match_trades.match_time'
            ])
            ->where(function ($query) use ($matchEngineOrderId) {
                $query->where('match_trades.buy_order_id', $matchEngineOrderId)
                      ->orWhere('match_trades.sell_order_id', $matchEngineOrderId);
            })
            ->where(function ($query) use ($userId) {
                $query->where('match_trades.buy_user_id', $userId)
                      ->orWhere('match_trades.sell_user_id', $userId);
            })
            ->where('match_trades.market_type', MarketType::MARGIN->value)
            ->orderBy('match_trades.match_time', 'desc')
            ->get();

        // 格式化成交数据
        return $trades->map(function ($trade) {
            return [
                'price' => $trade->price,
                'quantity' => $trade->quantity,
                'trade_time' => $trade->match_time
            ];
        })->toArray();
    }

    /**
     * 获取交易历史
     */
    public function getTradeHistory(int $userId, ?int $currencyId = null, ?int $marginMode = null, int $perPage = 20, int $page = 1): array
    {
        // 构建查询条件
        $query = $this->matchTrade->query()
            ->select([
                'match_trades.price',
                'match_trades.quantity',
                'match_trades.match_time',
                'match_trades.buy_order_id',
                'match_trades.sell_order_id',
                'match_trades.buy_user_id',
                'match_trades.sell_user_id',
                'currency.symbol',
                'buy_orders.order_type as buy_order_type',
                'buy_orders.side as buy_side',
                'sell_orders.order_type as sell_order_type',
                'sell_orders.side as sell_side'
            ])
            ->leftJoin('currency', 'match_trades.currency_id', '=', 'currency.id')
            ->leftJoin('match_orders as buy_orders', 'match_trades.buy_order_id', '=', 'buy_orders.order_id')
            ->leftJoin('match_orders as sell_orders', 'match_trades.sell_order_id', '=', 'sell_orders.order_id')
            ->where(function ($query) use ($userId) {
                $query->where('match_trades.buy_user_id', $userId)
                      ->orWhere('match_trades.sell_user_id', $userId);
            })
            ->where('match_trades.market_type', MarketType::MARGIN->value)
            ->orderBy('match_trades.match_time', 'desc');

        // 添加币种筛选
        if ($currencyId) {
            $query->where('match_trades.currency_id', $currencyId);
        }

        // 获取总数
        $total = $query->count();

        // 分页查询
        $trades = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        // 格式化数据
        $data = $trades->map(function ($trade) use ($userId) {
            return $this->formatTradeData($trade, $userId);
        })->toArray();

        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }

    /**
     * 格式化成交数据
     */
    private function formatTradeData($trade, int $userId): array
    {
        // 判断用户是买方还是卖方
        $isBuyer = $trade->buy_user_id == $userId;

        // 获取用户对应的订单类型和方向
        $orderType = $isBuyer ? $trade->buy_order_type : $trade->sell_order_type;
        $side = $isBuyer ? $trade->buy_side : $trade->sell_side;

        return [
            'symbol' => $trade->symbol ?? 'UNKNOWN',
            'side' => $side,
            'side_name' => $this->getSideName($side),
            'order_type' => $orderType,
            'order_type_name' => $this->getOrderTypeName($orderType),
            'price' => $trade->price,
            'quantity' => $trade->quantity,
            'trade_time' => $trade->match_time,
            'is_buyer' => $isBuyer
        ];
    }

    /**
     * 批量撤销订单
     */
    public function cancelAllOrders(int $userId, ?int $currencyId = null, ?int $marginMode = null): array
    {
        // TODO: 实现批量撤单逻辑
        return [
            'canceled_count' => 0,
            'failed_count' => 0
        ];
    }

    /**
     * 验证交易对配置
     */
    private function validateTradingPair(int $currencyId): void
    {
        $key = CurrencyConfigKey::getCurrencyKey($currencyId);
        $is_marginTrade = $this->redis->hget($key,'is_marginTrade');
        
        if (!$is_marginTrade) {
            throw new BusinessException(ResultCode::FAIL, '交易对配置不存在');
        }
    }

    /**
     * 验证用户配置
     */
    private function validateUserConfig(int $userId, int $marginMode): void
    {
        $userConfig = $this->userPerpetualConfig->query()
            ->where('user_id', $userId)
            ->first();

        if ($userConfig && $userConfig->margin_type != $marginMode) {
            throw new BusinessException(ResultCode::FAIL, '下单保证金模式与用户设置不符');
        }
    }

    /**
     * 获取交易配置
     */
    private function getTradeConfig(int $currencyId): TradeConfig
    {
        $config = $this->tradeConfig->query()
            ->where('currency_id', $currencyId)
            ->where('market_type', MarketType::MARGIN->value)
            ->first();

        if (!$config) {
            throw new BusinessException(ResultCode::FAIL, '交易配置不存在');
        }

        return $config;
    }

    /**
     * 验证订单参数
     */
    private function validateOrderParams(array $orderData, TradeConfig $tradeConfig): void
    {
        $quantity = (float)$orderData['quantity'];
        $price = (float)($orderData['price'] ?? 0);
        $orderType = (int)$orderData['order_type'];
        $currencyId = (int)$orderData['currency_id'];
        $side = (int)$orderData['side'];

        // 验证数量范围
        if (bccomp((string)$quantity, (string)$tradeConfig->min_trade_num, 18) < 0 ||
            bccomp((string)$quantity, (string)$tradeConfig->max_trade_num, 18) > 0) {
            throw new BusinessException(ResultCode::FAIL, '下单数量超出限制');
        }

        // 限价单验证价格
        if ($orderType == ContractOrderType::LIMIT->value) {
            if (bccomp((string)$price, '0', 18) <= 0) {
                throw new BusinessException(ResultCode::FAIL, '限价单价格必须大于0');
            }
            if (bccomp((string)$price, (string)$tradeConfig->min_trade_price, 18) < 0 ||
                bccomp((string)$price, (string)$tradeConfig->max_trade_price, 18) > 0) {
                throw new BusinessException(ResultCode::FAIL, '下单价格超出限制');
            }
        } else {
            // 市价单需要获取当前市场价格进行验证
            $marketPrice = $this->getCurrentMarketPrice($currencyId, $side);
            if (bccomp((string)$marketPrice, '0', 18) <= 0) {
                throw new BusinessException(ResultCode::FAIL, '当前市场价格异常，无法下市价单');
            }
        }
    }

    /**
     * 处理开仓订单
     */
    private function handleOpenOrder(int $userId, int $currencyId, array $orderData, TradeConfig $tradeConfig): array
    {
        $quantity = (float)$orderData['quantity'];
        $price = (float)($orderData['price'] ?? 0);
        $leverage = (float)$orderData['leverage'];
        $marginMode = (int)$orderData['margin_mode'];
        $side = (int)$orderData['side'];
        $orderType = (int)$orderData['order_type'];

        // 市价单需要获取当前市场价格进行保证金计算
        $calculationPrice = $price;
        if ($orderType == ContractOrderType::MARKET->value) {
            $calculationPrice = $this->getCurrentMarketPrice($currencyId, $side);
            // 市价单在订单记录中保持price为0，实际成交价格由撮合引擎确定
        }
        // 1. 计算基础保证金需求
        $baseMarginRequired = $this->calculateMarginRequirement($quantity, $calculationPrice, $leverage);

        // 2. 计算预估手续费
        $estimatedFee = $this->calculateEstimatedTradeFee($userId, $quantity, $calculationPrice, $orderType);

        // 3. 市价单需要额外冻结2%的保证金应对价格波动
        $marginRequired = $baseMarginRequired;
        $frozenAmount = (string)$baseMarginRequired;

        if ($orderType == ContractOrderType::MARKET->value) {
            // 市价单多冻结2%保证金
            $bufferAmount = bcmul((string)$baseMarginRequired, '0.02', 18);
            $frozenAmount = bcadd((string)$baseMarginRequired, $bufferAmount, 18);
            $marginRequired = (float)$frozenAmount;
        }

        // 4. 总冻结金额 = 保证金 + 手续费
        $totalFrozenAmount = bcadd($frozenAmount, (string)$estimatedFee, 18);
        $marginRequired = (float)$totalFrozenAmount;

        // 2. 检查用户风险等级
        $this->checkUserRiskLevel($userId, $currencyId, $quantity, $price, $leverage);
        
        // 3. 检查账户余额并调整开仓数量（如果余额不足）
        $adjustedResult = $this->checkAndAdjustOrderAmount($userId, $currencyId, $marginMode, $quantity, $calculationPrice, $leverage, $orderType, $marginRequired);

        // 如果数量被调整，需要重新计算相关金额
        if ($adjustedResult['quantity_adjusted']) {
            $quantity = $adjustedResult['final_quantity'];
            $baseMarginRequired = $adjustedResult['final_margin'];
            $estimatedFee = $adjustedResult['final_fee'];
            $totalFrozenAmount = $adjustedResult['final_frozen_amount'];
            $marginRequired = (float)$totalFrozenAmount;

            // 更新订单数据中的数量
            $orderData['quantity'] = $quantity;
        }

        // 4. 冻结保证金和手续费
        $this->freezeMargin($userId, $currencyId, $marginMode, $marginRequired);

        // 5. 创建订单记录
        $order = $this->createOrderRecord($userId, $currencyId, $orderData, $baseMarginRequired, (float)$totalFrozenAmount, $estimatedFee);

        // 6. 提交到撮合引擎
        $matchOrderId = $this->submitToMatchEngine($order, $orderData);

        // 7. 更新订单撮合ID
        $order->match_order_id = $matchOrderId;
        $order->save();

        return [
            'order_id' => $order->id,
            'match_order_id' => $matchOrderId,
            'status' => 'NEW',
            'created_at' => $order->created_at->toDateTimeString(),
        ];
    }

    /**
     * 处理平仓订单
     */
    private function handleCloseOrder(int $userId, int $currencyId, array $orderData, TradeConfig $tradeConfig): array
    {
        $quantity = (float)$orderData['quantity'];
        $price = (float)($orderData['price'] ?? 0);
        $marginMode = (int)$orderData['margin_mode'];
        $side = (int)$orderData['side'];
        $orderType = (int)$orderData['order_type'];

        // 市价单需要获取当前市场价格进行手续费计算
        $calculationPrice = $price;
        if ($orderType == ContractOrderType::MARKET->value) {
            $calculationPrice = $this->getCurrentMarketPrice($currencyId, $side);
        }

        // 1. 获取对应方向的持仓
        $positionSide = $this->getPositionSide($side);
        $position = $this->tradePerpetualPosition
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $positionSide)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if (!$position) {
            throw new BusinessException(ResultCode::FAIL, '没有可平仓位');
        }

        // 2. 检查可平仓数量
        if (bccomp((string)$quantity, (string)$position->available_quantity, 18) > 0) {
            throw new BusinessException(ResultCode::FAIL, '平仓数量超过可用持仓');
        }

        // 3. 冻结持仓
        $this->freezePosition($userId, $currencyId, $marginMode, $side, $quantity);

        // 4. 创建订单记录（平仓订单不需要保证金，但需要计算手续费）
        $estimatedFee = $this->calculateEstimatedTradeFee($userId, $quantity, $calculationPrice, $orderType);
        $order = $this->createOrderRecord($userId, $currencyId, $orderData, 0, $estimatedFee, $estimatedFee);

        // 5. 提交到撮合引擎
        $matchOrderId = $this->submitToMatchEngine($order, $orderData);

        // 6. 更新订单撮合ID
        $order->match_order_id = $matchOrderId;
        $order->save();

        return [
            'order_id' => $order->id,
            'match_order_id' => $matchOrderId,
            'status' => 'NEW',
            'created_at' => $order->created_at->toDateTimeString(),
        ];
    }

    /**
     * 计算保证金需求
     */
    private function calculateMarginRequirement(float $quantity, float $price, float $leverage): float
    {
        // 初始保证金 = 仓位名义价值 / 杠杆倍数
        $notionalValue = bcmul((string)$quantity, (string)$price, 18);
        return (float)bcdiv($notionalValue, (string)$leverage, 18);
    }

    /**
     * 计算预估手续费
     */
    private function calculateEstimatedTradeFee(int $userId, float $quantity, float $price, int $orderType): float
    {
        $feeRates = $this->getUserFuturesFeeRates($userId);

        // 限价单使用maker费率，市价单使用taker费率
        $feeRate = $orderType == ContractOrderType::LIMIT->value
            ? $feeRates['maker_fee_rate']
            : $feeRates['taker_fee_rate'];

        $notionalValue = bcmul((string)$quantity, (string)$price, 18);
        return (float)bcmul($notionalValue, (string)$feeRate, 18);
    }

    /**
     * 获取用户永续合约手续费率
     */
    private function getUserFuturesFeeRates(int $userId): array
    {
        $feeRates = UserVipLevel::getUserFeeRates($userId);

        if ($feeRates) {
            return [
                'maker_fee_rate' => $feeRates['futures_maker_fee_rate'],
                'taker_fee_rate' => $feeRates['futures_taker_fee_rate']
            ];
        }

        // 默认手续费率（如果用户没有VIP等级）
        return [
            'maker_fee_rate' => 0.0002, // 0.02% 挂单手续费
            'taker_fee_rate' => 0.0004  // 0.04% 吃单手续费
        ];
    }

    /**
     * 检查用户风险等级
     */
    private function checkUserRiskLevel(int $userId, int $currencyId, float $quantity, float $price, float $leverage): void
    {
        // 计算当前仓位总价值
        $currentPositionValue = $this->getCurrentPositionValue($userId, $currencyId);

        // 加上新开仓价值
        $newPositionValue = bcmul((string)$quantity, (string)$price, 18);
        $totalPositionValue = bcadd((string)$currentPositionValue, $newPositionValue, 18);

        // 查找对应的风险等级
        $marginLevel = $this->tradeMarginLevel
            ->where('currency_id', $currencyId)
            ->where('margin_min', '<=', (float)$totalPositionValue)
            ->where('margin_max', '>=', (float)$totalPositionValue)
            ->first();

        if (!$marginLevel) {
            throw new BusinessException(ResultCode::FAIL, '未找到对应的风险等级配置');
        }

        // 检查杠杆是否在允许范围内
        if (bccomp((string)$leverage, (string)$marginLevel->leverage_min, 18) < 0 ||
            bccomp((string)$leverage, (string)$marginLevel->leverage_max, 18) > 0) {
            throw new BusinessException(ResultCode::FAIL, "当前风险等级允许的杠杆范围为 {$marginLevel->leverage_min}-{$marginLevel->leverage_max} 倍");
        }
    }

    /**
     * 获取当前仓位总价值
     */
    private function getCurrentPositionValue(int $userId, int $currencyId): float
    {
        $positions = $this->tradePerpetualPosition
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('status', PositionStatus::HOLDING->value)
            ->get();

        $totalValue = '0';
        foreach ($positions as $position) {
            $positionValue = bcmul((string)$position->quantity, (string)$position->entry_price, 18);
            $totalValue = bcadd($totalValue, (string)$positionValue, 18);
        }

        return (float)$totalValue;
    }

    /**
     * 检查账户余额
     */
    private function checkAccountBalance(int $userId, int $currencyId, int $marginMode, float $marginRequired): void
    {
        // 永续合约使用合约账户
        $accountType = AccountType::FUTURES->value;

        // 获取计价币种ID
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        // 使用UserAccountsAssetService获取用户资产
        $asset = $this->userAccountsAssetService->getUserAsset(
            $userId,
            $accountType,
            $quoteCurrencyId
        );
        if (!$asset || bccomp((string)$asset->available, (string)$marginRequired, 18) < 0) {
            throw new BusinessException(ResultCode::FAIL, '账户余额不足');
        }
    }

    /**
     * 检查账户余额并调整订单金额
     */
    private function checkAndAdjustOrderAmount(int $userId, int $currencyId, int $marginMode, float $quantity, float $price, float $leverage, int $orderType, float $requiredAmount): array
    {
        // 永续合约使用合约账户
        $accountType = AccountType::FUTURES->value;
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        $asset = $this->userAccountsAssetService->getUserAsset($userId, $accountType, $quoteCurrencyId);

        if (!$asset) {
            throw new BusinessException(ResultCode::FAIL, '账户不存在');
        }

        $availableBalance = (float)$asset->available;

        // 如果余额充足，直接返回原始数量
        if (bccomp((string)$availableBalance, (string)$requiredAmount, 18) >= 0) {
            return [
                'quantity_adjusted' => false,
                'final_quantity' => $quantity,
                'final_margin' => $this->calculateMarginRequirement($quantity, $price, $leverage),
                'final_fee' => $this->calculateEstimatedTradeFee($userId, $quantity, $price, $orderType),
                'final_frozen_amount' => $requiredAmount
            ];
        }

        // 余额不足，需要调整开仓数量
        return $this->adjustOrderQuantityByBalance($userId, $currencyId, $marginMode, $availableBalance, $price, $leverage, $orderType);
    }

    /**
     * 冻结保证金
     */
    private function freezeMargin(int $userId, int $currencyId, int $marginMode, float $amount): void
    {
        $accountType = AccountType::FUTURES->value; // 永续合约使用合约账户
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        $this->userAccountsAssetService->freezeAsset(
            $userId,
            $accountType,
            $quoteCurrencyId, // 使用计价币种作为保证金
            $amount,
            FlowsType::PERPETUAL_MARGIN_FREEZE->value, // 永续合约保证金冻结
            0,
            'available',
            'frozen'
        );
    }

    /**
     * 解冻保证金
     */
    private function unfreezeMargin(int $userId, int $currencyId, int $marginMode, float $amount): void
    {
        $accountType = AccountType::FUTURES->value; // 永续合约使用合约账户
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        $this->userAccountsAssetService->unfreezeAsset(
            $userId,
            $accountType,
            $quoteCurrencyId, // 使用计价币种作为保证金
            $amount,
            FlowsType::PERPETUAL_MARGIN_UNFREEZE->value, // 永续合约保证金解冻
            0,
            'frozen',
            'available'
        );
    }

    /**
     * 冻结持仓
     */
    private function freezePosition(int $userId, int $currencyId, int $marginMode, int $side, float $quantity): void
    {
        $positionSide = $this->getPositionSide($side);
        $position = $this->tradePerpetualPosition
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $positionSide)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if ($position) {
            $position->available_quantity = (float)bcsub((string)$position->available_quantity, (string)$quantity, 18);
            $position->frozen_quantity = (float)bcadd((string)$position->frozen_quantity, (string)$quantity, 18);
            $position->save();
        }
    }

    /**
     * 解冻持仓
     */
    private function unfreezePosition(int $userId, int $currencyId, int $marginMode, int $side, float $quantity): void
    {
        $positionSide = $this->getPositionSide($side);
        $position = $this->tradePerpetualPosition
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $positionSide)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if ($position) {
            $position->available_quantity = (float)bcadd((string)$position->available_quantity, (string)$quantity, 18);
            $position->frozen_quantity = (float)bcsub((string)$position->frozen_quantity, (string)$quantity, 18);
            $position->save();
        }
    }

    /**
     * 获取仓位方向
     */
    private function getPositionSide(int $side): int
    {
        // 买入平空、卖出平多 对应的持仓方向
        return match($side) {
            ContractSide::BUY_CLOSE->value => 2, // 平空对应空头仓位
            ContractSide::SELL_CLOSE->value => 1, // 平多对应多头仓位
            default => 0
        };
    }

    /**
     * 创建订单记录
     */
    private function createOrderRecord(int $userId, int $currencyId, array $orderData, float $marginAmount, float $frozenAmount = 0, float $estimatedFee = 0): TradePerpetualOrder
    {
        $order = new TradePerpetualOrder();
        $order->user_id = $userId;
        $order->currency_id = $currencyId;
        $order->match_order_id = 0; // 临时值，后续更新
        $order->margin_mode = (int)$orderData['margin_mode'];
        $order->side = (int)$orderData['side'];
        $order->price = (float)($orderData['price'] ?? 0);
        $order->margin_amount = $marginAmount;
        $order->frozen_amount = $frozenAmount > 0 ? $frozenAmount : $marginAmount; // 冻结金额
        $order->used_amount = 0; // 初始已使用金额为0
        $order->estimated_fee = $estimatedFee; // 预估手续费
        $order->actual_fee = 0; // 初始实际手续费为0
        $order->position_id = 0; // 初始仓位ID为0，成交时关联
        $order->leverage = (float)$orderData['leverage'];
        $order->reduce_only = (int)($orderData['reduce_only'] ?? 0);
        $order->stop_price = (float)($orderData['stop_price'] ?? 0);
        $order->trigger_type = (int)($orderData['trigger_type'] ?? 0);
        $order->save();

        return $order;
    }

    /**
     * 提交订单到撮合引擎
     */
    private function submitToMatchEngine(TradePerpetualOrder $order, array $orderData): int
    {
        $symbol = $this->getCurrencySymbol($order->currency_id);

        // 1. 先创建撮合引擎订单记录
        $matchOrder = new MatchOrder();
        $matchOrder->user_id = $order->user_id;
        $matchOrder->currency_id = $order->currency_id;
        $matchOrder->order_id = $this->idGenerator->generate();
        $matchOrder->market_type = MarketType::MARGIN->value;
        $matchOrder->side = $order->side == ContractSide::BUY_OPEN->value || $order->side == ContractSide::BUY_CLOSE->value ? 1 : -1;
        $matchOrder->quantity = (float)$orderData['quantity']; // 从订单数据获取
        $matchOrder->fill_quantity = 0;
        $matchOrder->price = $order->price;
        $matchOrder->avg_price = 0;
        $matchOrder->order_type = (int)$orderData['order_type'] == ContractOrderType::LIMIT->value ? 2 : 1; // 1市价，2限价
        $matchOrder->trade_type = 0; // 0待成交
        $matchOrder->order_force = $this->getTimeInForce((int)($orderData['time_in_force'] ?? 1));
        $matchOrder->fill_time = 0;
        $matchOrder->status = -1; // -1待加入撮合引擎
        $matchOrder->reason = '';
        $matchOrder->has_trade = 0;
        $matchOrder->save();

        $engineData = [
            'order_id' => $matchOrder->getOrderId(),
            'user_id' => (string)$matchOrder->getUserId(),
            'side' => $order->side == ContractSide::BUY_OPEN->value || $order->side == ContractSide::BUY_CLOSE->value ? 'buy' : 'sell',
            'type' => (int)$orderData['order_type'] == ContractOrderType::LIMIT->value ? 'limit' : 'market',
            'quantity' => $matchOrder->getQuantity(),
            'time_in_force' => $matchOrder->getOrderForce(),
            'leverage' => 1.0, // 杠杆订单暂时传1.0，实际杠杆在业务层处理
            'market_type' => MarketType::getMarketString(MarketType::CRYPTO->value),
            'symbol' => $symbol,
            'is_bot' => 0
        ];

        // 3. 提交到撮合引擎
        $messageId = $this->matchEngineService->addOrder(
            MarketType::MARGIN->value,
            $symbol,
            $order->user_id,
            $engineData
        );

        if (!$messageId) {
            throw new BusinessException(ResultCode::FAIL, '提交订单到撮合引擎失败');
        }

        return $matchOrder->id; // 返回撮合订单ID
    }

    /**
     * 从撮合引擎撤销订单
     */
    private function cancelOrderInMatchEngine(string $symbol, string $orderId, int $userId): bool
    {
        try {
            // 直接调用撮合引擎服务撤单
            return $this->matchEngineService->cancelOrder(
                MarketType::MARGIN->value,
                $symbol,
                $orderId,
                $userId
            );
        } catch (\Exception $e) {
            $this->logger->error('撤单失败', [
                'symbol' => $symbol,
                'order_id' => $orderId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }



    /**
     * 获取币种符号
     */
    private function getCurrencySymbol(int $currencyId): string
    {
        $key = CurrencyConfigKey::getCurrencyKey($currencyId);
        $symbol = $this->redis->hGet($key, 'symbol');

        if (!$symbol) {
            throw new BusinessException(ResultCode::FAIL, '币种不存在或符号为空');
        }

        return $symbol;
    }

    /**
     * 获取计价币种ID
     */
    private function getQuoteCurrencyId(int $currencyId): int
    {
        $key = CurrencyConfigKey::getCurrencyKey($currencyId);
        $quoteAssetsId = $this->redis->hGet($key, 'quote_assets_id');

        if (!$quoteAssetsId) {
            throw new BusinessException(ResultCode::FAIL, '币种不存在或计价币种ID为空');
        }

        return (int)$quoteAssetsId;
    }

    /**
     * 获取时间有效期字符串
     */
    private function getTimeInForce(int $timeInForce): string
    {
        return match($timeInForce) {
            // Good Till Cancel
            2 => 'ioc', // Immediate Or Cancel
            3 => 'fok', // Fill Or Kill
            default => 'gtc'
        };
    }

    /**
     * 获取当前市场价格（用于市价单）
     */
    private function getCurrentMarketPrice(int $currencyId, int $side): float
    {
        // 使用外部成交数据获取市场价格 marketdata:trade-out:{币种id}
        $priceKey = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::MARGIN->value);
        $price = $this->redis->hGet($priceKey, 'price');
        if (!$price || $price <= 0) {
            throw new BusinessException(ResultCode::FAIL, '无法获取市场价格');
        }
        return (float)$price;
    }

    /**
     * 格式化订单数据
     */
    private function formatOrderData($order): array
    {
        return [
            'order_id' => $order->id,
            'symbol' => $order->symbol ?? 'UNKNOWN',
            'side' => $order->side,
            'side_name' => $this->getSideName($order->side),
            'leverage' => $order->leverage,
            'margin_mode' => $order->margin_mode,
            'margin_mode_name' => $this->getMarginModeName($order->margin_mode),
            'order_type' => $order->match_order_type ?? 1,
            'order_type_name' => $this->getOrderTypeName($order->match_order_type ?? 1),
            'price' => $order->price,
            'quantity' => $order->order_quantity ?? 0,
            'filled_quantity' => $order->fill_quantity ?? 0,
            'avg_price' => $order->avg_price ?? 0,
            'fee' => $order->actual_fee ?? 0,
            'reduce_only' => $order->reduce_only,
            'status' => $order->order_status ?? 1,
            'status_name' => $this->getOrderStatusName($order->order_status ?? 1),
            'created_at' => $order->created_at,
        ];
    }

    /**
     * 获取合约方向名称
     */
    private function getSideName(int $side): string
    {
        return match($side) {
            1 => '买入开多',
            2 => '卖出开空',
            3 => '买入平空',
            4 => '卖出平多',
            default => '未知'
        };
    }

    /**
     * 获取保证金模式名称
     */
    private function getMarginModeName(int $marginMode): string
    {
        return match($marginMode) {
            1 => '全仓',
            2 => '逐仓',
            default => '未知'
        };
    }

    /**
     * 获取订单类型名称
     */
    private function getOrderTypeName(int $orderType): string
    {
        return match($orderType) {
            1 => '市价单',
            2 => '限价单',
            default => '未知'
        };
    }

    /**
     * 获取订单状态名称
     */
    private function getOrderStatusName(int $status): string
    {
        return match($status) {
            -1 => '待提交',
            0 => '已取消',
            1 => '等待成交',
            2 => '部分成交',
            3 => '全部成交',
            default => '未知'
        };
    }

    /**
     * 根据可用余额调整订单数量
     */
    private function adjustOrderQuantityByBalance(int $userId, int $currencyId, int $marginMode, float $availableBalance, float $price, float $leverage, int $orderType): array
    {
        // 如果是全仓模式，需要考虑现有仓位的保证金
        if ($marginMode == MarginMode::CROSS->value) {
            return $this->adjustCrossMarginOrder($userId, $currencyId, $availableBalance, $price, $leverage, $orderType);
        } else {
            // 逐仓模式，只能使用可用余额
            return $this->adjustIsolatedMarginOrder($userId, $availableBalance, $price, $leverage, $orderType);
        }
    }

    /**
     * 调整全仓模式订单
     */
    private function adjustCrossMarginOrder(int $userId, int $currencyId, float $availableBalance, float $price, float $leverage, int $orderType): array
    {
        // 获取用户在该币种的所有全仓持仓的总保证金
        $existingMargin = $this->getUserCrossMarginAmount($userId, $currencyId);

        // 总可用资金 = 账户余额 + 现有保证金
        $totalAvailableFunds = bcadd((string)$availableBalance, (string)$existingMargin, 18);

        // 计算最大可开仓数量
        return $this->calculateMaxOrderQuantity($userId, (float)$totalAvailableFunds, $price, $leverage, $orderType);
    }

    /**
     * 调整逐仓模式订单
     */
    private function adjustIsolatedMarginOrder(int $userId, float $availableBalance, float $price, float $leverage, int $orderType): array
    {
        // 逐仓模式只能使用账户可用余额
        return $this->calculateMaxOrderQuantity($userId, $availableBalance, $price, $leverage, $orderType);
    }

    /**
     * 获取用户全仓模式下的总保证金
     */
    private function getUserCrossMarginAmount(int $userId, int $currencyId): float
    {
        $positions = $this->tradePerpetualPosition
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', MarginMode::CROSS->value)
            ->where('status', PositionStatus::HOLDING->value)
            ->get();

        $totalMargin = '0';
        foreach ($positions as $position) {
            $totalMargin = bcadd($totalMargin, (string)$position->margin_amount, 18);
        }

        return (float)$totalMargin;
    }

    /**
     * 计算最大可开仓数量
     */
    private function calculateMaxOrderQuantity(int $userId, float $availableFunds, float $price, float $leverage, int $orderType): array
    {
        if (bccomp((string)$availableFunds, '0', 18) <= 0) {
            throw new BusinessException(ResultCode::FAIL, '可用资金不足，无法开仓');
        }

        // 获取手续费率
        $feeRates = $this->getUserFuturesFeeRates($userId);
        $feeRate = $orderType == ContractOrderType::LIMIT->value
            ? $feeRates['maker_fee_rate']
            : $feeRates['taker_fee_rate'];

        // 计算最大数量：可用资金 / (价格/杠杆 + 价格*手续费率)
        // 即：可用资金 / (保证金率 + 手续费率) / 价格
        $marginRate = bcdiv('1', (string)$leverage, 18);
        $totalRate = bcadd($marginRate, (string)$feeRate, 18);
        $costPerUnit = bcmul((string)$price, $totalRate, 18);

        $maxQuantity = bcdiv((string)$availableFunds, $costPerUnit, 18);

        if (bccomp($maxQuantity, '0', 18) <= 0) {
            throw new BusinessException(ResultCode::FAIL, '可用资金不足，无法开仓');
        }

        // 计算调整后的各项金额
        $finalQuantity = (float)$maxQuantity;
        $finalMargin = $this->calculateMarginRequirement($finalQuantity, $price, $leverage);
        $finalFee = $this->calculateEstimatedTradeFee($userId, $finalQuantity, $price, $orderType);
        $finalFrozenAmount = bcadd((string)$finalMargin, (string)$finalFee, 18);

        return [
            'quantity_adjusted' => true,
            'final_quantity' => $finalQuantity,
            'final_margin' => $finalMargin,
            'final_fee' => $finalFee,
            'final_frozen_amount' => (float)$finalFrozenAmount
        ];
    }
}
