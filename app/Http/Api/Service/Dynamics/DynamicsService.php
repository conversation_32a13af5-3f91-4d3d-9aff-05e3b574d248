<?php

declare(strict_types=1);
/**
 * DynamicsService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Service\Dynamics;

use App\Http\Api\Request\Dynamics\DynamicsRequest;
use App\Model\Article\Dynamics;
use App\Model\Article\DynamicsCurrency;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\DynamicsRepository;
use Hyperf\Di\Annotation\Inject;

class DynamicsService
{
    #[Inject]
    protected DynamicsRepository $repository;
    /**
     * 查询动态列表
     * Summary of list
     * @return array
     */
    public function list(DynamicsRequest $request)
    {
        $page = $request->input('page',default: 1);
        $pageSize = $request->input('page_size',10);
        // $params = $request->all();
        // $params = array_merge($params, [
        //     'pid_id' => 0,
        // ]);
        // return $this->repository->page($params,$page,$pageSize);
        // $query = Dynamics::query();
        // $query->where('pid_id',0);
        // $query->orderBy('id','desc');
        // $query->with(['user:id,display_name,avatar','hottopic']);
        // return $query->paginate($page, $pageSize);

        //通过权限过滤 公开，关注我的【】

        return QueryBuilder::for(Dynamics::class, $request)
                ->with(['user:id,display_name,avatar','hottopic'])
                ->filters(['type', 'title'])
                ->orderByDesc('id')
                ->allowedSorts(['id', 'created_at'])
                ->pagex(function($item){
                    if($item->dynamics_currency){
                        $item->dynamics_currency = DynamicsCurrency::query()->whereIn('id',$item->dynamics_currency)->get();
                    }else{
                        $item->dynamics_currency = [];
                    }
                    return $item;
                });
    }

    /**
     * 获取动态详情
     * Summary of detail
     * @param mixed $id
     */
    public function detail($id) {
        $this->repository->getModel()->increment(Dynamics::FIELD_LOOK_NUM);
        $detail = $this->repository->getModel()->with(['user:id,display_name,avatar'])->where('id',$id)->first();
        if ($detail) {
            $detail->makeHidden([
                Dynamics::FIELD_COLLECT_UIDS,
                Dynamics::FIELD_FORWARD_UIDS,
                Dynamics::FIELD_CONCERN_UIDS,
                Dynamics::FIELD_USER_ID
            ]);
        }
        return $detail;
    }

    /**
     * 发布动态
     * Summary of create
     * @param mixed $request
     */
    public function create(DynamicsRequest $request) {
        $params = $request->all();
        $params['user_id'] = $request->userId();
        return $this->repository->create($params);
    }

    /**
     * 评论
     * Summary of comment
     * @param mixed $request
     */
    public function comment(DynamicsRequest $request) {
        $params = $request->all();
        $params['pid_id'] = $params['dynamics_id'];
        $params['user_id'] = $request->userId();
        unset( $params['dynamics_id']);
        return $this->repository->create($params);
    }

    /**
     * 评论列表
     * Summary of comment
     * @param mixed $request
     */
    public function commentList(DynamicsRequest $request) {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        $params = $request->all();
        $params = array_merge($params, [
            'pid_id' => $params['dynamics_id'],
        ]);
        return $this->repository->page($params,$page,$pageSize);
    }

    /**
     * 点赞
     * Summary of liked
     * @param mixed $request
     */
    public function liked(DynamicsRequest $request) {
        $params = $request->all();
        return $this->repository->getModel()->where('id',$params['dynamics_id'])->increment('liked');
    }

    /**
     * 关注
     * Summary of comment
     * @param mixed $request
     */
    public function concern(DynamicsRequest $request): bool {
        $params = $request->all();
        $concern = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('concern_uids');
        // 判断是否已关注 $concern 是否包含当前用户
        $concern = $concern ?? [];
        if(!$concern) {
            $concern = [$request->userId()];
        }else if (!in_array($request->userId(),$concern)) {
            $concern = array_merge($concern,[$request->userId()]);
        }
        $concern = json_encode($concern);
        $this->repository->getModel()->where('id',$params['dynamics_id'])->update(['concern_uids'=>$concern]);
        return true;
    }

    /**
     * 取消关注
     * Summary of comment
     * @param mixed $request
     */
    public function unConcern(DynamicsRequest $request): bool {
        $params = $request->all();
        $concern = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('concern_uids');
        // 判断是否已关注 $concern 是否包含当前用户
        $concern = $concern ?? [];
        if (in_array($request->userId(), $concern)) {
            $key = array_search($request->userId(), $concern);
            if ($key !== false) {
                unset($concern[$key]);
            }
            $this->repository->getModel()->where('id', $params['dynamics_id'])->update(['concern_uids' => json_encode(array_values($concern))]);
        }
        return true;
    }

    /**
     * 转发
     * Summary of forward
     * @param mixed $request
     */
    public function forward(DynamicsRequest $request): bool {
        $params = $request->all();
        $forward = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('forward_uids');
        // 判断是否已关注 $forward 是否包含当前用户
        $forward = $forward ?? [];
        if(!$forward) {
            $forward = [$request->userId()];
        }else if (!in_array($request->userId(),$forward)) {
            $forward = array_merge($forward,[$request->userId()]);
        }
        $forward = json_encode($forward);
        $this->repository->getModel()->where('id',$params['dynamics_id'])->update(['forward_uids'=>$forward]);
        return true;
    }

    /**
     * 取消转发
     * Summary of unForward
     * @param mixed $request
     */
    public function unForward(DynamicsRequest $request): bool {
        $params = $request->all();
        $forward = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('forward_uids');
        // 判断是否已关注 $forward 是否包含当前用户
        $forward = $forward ?? [];
        if (in_array($request->userId(), $forward)) {
            $key = array_search($request->userId(), $forward);
            if ($key !== false) {
                unset($forward[$key]);
            }
            $this->repository->getModel()->where('id', $params['dynamics_id'])->update(['forward_uids' => json_encode(array_values($forward))]);
        }
        return true;
    }
    /**
     * 收藏
     * Summary of collect
     * @param mixed $request
     */
    public function collect(DynamicsRequest $request): bool {
        $params = $request->all();
        $collect = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('collect_uids');
        // 判断是否已关注 $collect 是否包含当前用户
        $collect = $collect ?? [];
        if(!$collect) {
            $collect = [$request->userId()];
        }else if (!in_array($request->userId(),$collect)) {
            $collect = array_merge($collect,[$request->userId()]);
        }
        $collect = json_encode($collect);
        $this->repository->getModel()->where('id',$params['dynamics_id'])->update(['collect_uids'=>$collect]);
        return true;
    }

    /**
     * 取消收藏
     * Summary of unCollect
     * @param mixed $request
     */
    public function unCollect(DynamicsRequest $request): bool {
        $params = $request->all();
        $collect = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('collect_uids');
        // 判断是否已关注 $collect 是否包含当前用户
        $collect = $collect ?? [];
        if (in_array($request->userId(), $collect)) {
            $key = array_search($request->userId(), $collect);
            if ($key !== false) {
                unset($collect[$key]);
            }
            $this->repository->getModel()->where('id', $params['dynamics_id'])->update(['collect_uids' => json_encode(array_values($collect))]);
        }
        return true;
    }


}
