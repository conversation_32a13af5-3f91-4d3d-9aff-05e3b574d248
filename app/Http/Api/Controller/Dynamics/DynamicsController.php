<?php

declare(strict_types=1);

/**
 * DynamicsController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Controller\Dynamics;

use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use App\Http\Api\Request\Dynamics\DynamicsRequest;
use \App\Http\Api\Service\Dynamics\DynamicsService;
use Hyperf\HttpServer\Annotation\PostMapping;

/**
 * 动态api
 * @Controller
 */
#[Controller(prefix: "api/dynamics/dynamics")]
class DynamicsController extends AbstractController
{
    
    
    #[Inject]
    protected DynamicsService $dynamicsService;

    /**
     * 说明：动态列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->list($request);
        return $this->success($result);
    }

    /**
     * 说明：动态详情
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("detail")]
    public function detail(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->detail($request->input('dynamics_id'));
        return $this->success($result);
    }

    /**
     * 说明：动态发布
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("create")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function create(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->create($request);
        return $this->success($result);
    }

    /**
     * 说明：评论
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("comment")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function comment(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->comment($request);
        return $this->success($result);
    }

    /**
     * 说明：评论列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("commentList")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function commentList(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->commentList($request);
        return $this->success($result);
    }

    /**
     * 说明：点赞
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("liked")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function liked(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->liked($request);
        return $this->success($result);
    }

    /**
     * 说明：关注动态
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("concern")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function concern(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->concern($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：取消关注
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unConcern")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unConcern(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->unConcern($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：转发
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("forward")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function forward(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->forward($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：取消转发
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unForward")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unForward(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->unForward($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：收藏
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("collect")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function collect(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->collect($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：取消收藏
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unCollect")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unCollect(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->unCollect($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }


}
