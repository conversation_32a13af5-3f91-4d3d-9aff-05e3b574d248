<?php

declare(strict_types=1);

namespace App\Service\UserAccounts;

use App\Enum\AsyncExecutorKey;
use App\Job\AsyncFunExecutorJob;
use App\Job\User\UserAssetsChangedCacheJob;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserAccountsFlow;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

/**
 * 用户账户资金变动服务类
 */
class UserAccountsAssetService
{
    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('user-accounts','account-logs');
    }

    /**
     * 冻结用户资金
     * 将指定字段资金转移到指定冻结字段中
     */
    public function freezeAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available',
        string $frozenField = 'frozen'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('冻结金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $frozenField) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够
            if (bccomp((string)$asset->{$assetField}, (string)$amount, 18) < 0) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeAsset = $asset->{$assetField};
            $beforeFrozen = $asset->{$frozenField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcsub((string)$asset->{$assetField}, (string)$amount, 18);
            $asset->{$frozenField} = (float)bcadd((string)$asset->{$frozenField}, (string)$amount, 18);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAsset,
                $asset->{$assetField},
                -1, // 减少
                $relatedId
            );

            // 记录冻结资金增加流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeFrozen,
                $asset->{$frozenField},
                1, // 增加
                $relatedId
            );

            go(function ()use($userId,$accountType,$currencyId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('冻结资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'asset_field' => $assetField,
                'frozen_field' => $frozenField,
                'before_' . $assetField => $beforeAsset,
                'after_' . $assetField => $asset->{$assetField},
                'before_' . $frozenField => $beforeFrozen,
                'after_' . $frozenField => $asset->{$frozenField},
            ]);

            return true;
        });
    }

    /**
     * 解冻用户资金
     * 将指定冻结字段资金转移到指定目标字段中
     */
    public function unfreezeAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $frozenField = 'frozen',
        string $targetField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('解冻金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $frozenField, $targetField) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够
            if (bccomp((string)$asset->{$frozenField}, (string)$amount, 18) < 0) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeTarget = $asset->{$targetField};
            $beforeFrozen = $asset->{$frozenField};

            // 更新资产余额
            $asset->{$frozenField} = (float)bcsub((string)$asset->{$frozenField}, (string)$amount, 18);
            $asset->{$targetField} = (float)bcadd((string)$asset->{$targetField}, (string)$amount, 18);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeFrozen,
                $asset->{$frozenField},
                -1, // 减少
                $relatedId
            );

            // 记录目标字段增加流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeTarget,
                $asset->{$targetField},
                1, // 增加
                $relatedId
            );

            go(function ()use($userId,$accountType,$currencyId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('解冻资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'frozen_field' => $frozenField,
                'target_field' => $targetField,
                'before_' . $targetField => $beforeTarget,
                'after_' . $targetField => $asset->{$targetField},
                'before_' . $frozenField => $beforeFrozen,
                'after_' . $frozenField => $asset->{$frozenField},
            ]);

            return true;
        });
    }

    /**
     * 扣减冻结资金
     * 直接从冻结资金中扣除
     */
    public function deductFrozenAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'frozen'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('扣减金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够
            if (bccomp((string)$asset->{$assetField}, (string)$amount, 18) < 0) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcsub((string)$asset->{$assetField}, (string)$amount, 18);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                -1, // 减少
                $relatedId
            );

            go(function ()use($accountType,$currencyId,$userId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('扣减资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'before_' . $assetField => $beforeAmount,
                'after_' . $assetField => $asset->{$assetField},
            ]);

            return true;
        });
    }

    /**
     * 增加用户可用资金
     */
    public function addAvailableAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('增加金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField) {
            // 获取或创建用户账户资产记录（加锁）
            $asset = $this->getOrCreateAsset($userId, $accountType, $currencyId);

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcadd((string)$asset->{$assetField}, (string)$amount, 18);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金增加流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                1, // 增加
                $relatedId
            );

            go(function ()use($accountType,$currencyId,$userId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('增加资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'before_' . $assetField => $beforeAmount,
                'after_' . $assetField => $asset->{$assetField},
            ]);

            return true;
        });
    }

    /**
     * 扣减用户可用资金
     */
    public function deductAvailableAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('扣减金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够
            if (bccomp((string)$asset->{$assetField}, (string)$amount, 18) < 0) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcsub((string)$asset->{$assetField}, (string)$amount, 18);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                -1, // 减少
                $relatedId
            );

            go(function ()use($accountType,$currencyId,$userId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('扣减资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'before_' . $assetField => $beforeAmount,
                'after_' . $assetField => $asset->{$assetField},
            ]);

            return true;
        });
    }

    /**
     * 增加用户借款金额
     */
    public function addBorrowedAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'borrowed_amount',
        string $availableField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('借款金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField,$availableField) {
            // 获取或创建用户账户资产记录（加锁）
            $asset = $this->getOrCreateAsset($userId, $accountType, $currencyId);

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField} ?? 0;
            $beforeAvailable = $asset->{$availableField};

            // 更新指定字段和可用余额
            $asset->{$assetField} = (float)bcadd((string)$beforeAmount, (string)$amount, 18);
            $asset->{$availableField} = (float)bcadd((string)$asset->{$availableField}, (string)$amount, 18);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录指定字段增加流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                1, // 增加
                $relatedId
            );

            // 记录可用资金增加流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAvailable,
                $asset->{$availableField},
                1, // 增加
                $relatedId
            );

            go(function ()use($accountType,$currencyId,$userId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('增加资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'before_' . $assetField => $beforeAmount,
                'after_' . $assetField => $asset->{$assetField},
                'before_available' => $beforeAvailable,
                'after_available' => $asset->{$availableField},
            ]);

            return true;
        });
    }

    /**
     * 减少用户借款金额（还款）
     */
    public function reduceBorrowedAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'borrowed_amount'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('还款金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            $beforeAmount = $asset->{$assetField} ?? 0;
            $beforeAvailable = $asset->available;

            // 检查指定字段金额是否足够
            if (bccomp((string)$beforeAmount, (string)$amount, 18) < 0) {
                throw new \RuntimeException('余额不足');
            }

            // 检查可用余额是否足够
            if (bccomp((string)$asset->available, (string)$amount, 18) < 0) {
                throw new \RuntimeException('可用余额不足');
            }

            // 更新指定字段和可用余额
            $asset->{$assetField} = (float)bcsub((string)$beforeAmount, (string)$amount, 18);
            $asset->available = (float)bcsub((string)$asset->available, (string)$amount, 18);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录指定字段减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                -1, // 减少
                $relatedId
            );

            // 记录可用资金减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAvailable,
                $asset->available,
                -1, // 减少
                $relatedId
            );

            go(function ()use($accountType,$currencyId,$userId){
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($userId,$accountType,$currencyId),5);
            });

            $this->logger->info('减少资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'before_' . $assetField => $beforeAmount,
                'after_' . $assetField => $asset->{$assetField},
                'before_available' => $beforeAvailable,
                'after_available' => $asset->available,
            ]);

            return true;
        });
    }

    /**
     * 增加用户利息金额
     */
    public function addInterestAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'interest_amount'
    ): bool {
        if (bccomp((string)$amount, '0', 18) <= 0) {
            throw new \InvalidArgumentException('利息金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField) {
            // 获取或创建用户账户资产记录（加锁）
            $asset = $this->getOrCreateAsset($userId, $accountType, $currencyId);

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField} ?? 0;

            // 更新指定字段金额
            $asset->{$assetField} = (float)bcadd((string)$beforeAmount, (string)$amount, 18);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录指定字段增加流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                1, // 增加
                $relatedId
            );

            $this->logger->info('增加资金成功', [
                'user_id' => $userId,
                'account_type' => $accountType,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'before_' . $assetField => $beforeAmount,
                'after_' . $assetField => $asset->{$assetField},
            ]);

            return true;
        });
    }

    /**
     * 获取用户账户资产
     */
    public function getUserAsset(int $userId, int $accountType, int $currencyId): ?UserAccountsAsset
    {
        return UserAccountsAsset::query()
            ->where('user_id', $userId)
            ->where('account_type', $accountType)
            ->where('currency_id', $currencyId)
            ->first();
    }

    /**
     * 获取用户所有账户资产
     */
    public function getUserAssets(int $userId, ?int $accountType = null): array
    {
        $query = UserAccountsAsset::query()->where('user_id', $userId);

        if ($accountType !== null) {
            $query->where('account_type', $accountType);
            $query->where('available', '>', 0);
            $query->where('status', '=', 1);
        }

        return $query->get()->toArray();
    }

    /**
     * 检查用户资金是否足够
     */
    public function checkAvailableBalance(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount
    ): bool {
        $asset = $this->getUserAsset($userId, $accountType, $currencyId);

        if (!$asset) {
            return false;
        }

        return bccomp((string)$asset->available, (string)$amount, 18) >= 0;
    }

    /**
     * 检查用户冻结资金是否足够
     */
    public function checkFrozenBalance(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount
    ): bool {
        $asset = $this->getUserAsset($userId, $accountType, $currencyId);

        if (!$asset) {
            return false;
        }

        return bccomp((string)$asset->frozen, (string)$amount, 18) >= 0;
    }

    /**
     * 获取或创建用户账户资产记录
     */
    protected function getOrCreateAsset(int $userId, int $accountType, int $currencyId): UserAccountsAsset
    {
        $asset = UserAccountsAsset::query()
            ->where('user_id', $userId)
            ->where('account_type', $accountType)
            ->where('currency_id', $currencyId)
            ->lockForUpdate()
            ->first();

        if (!$asset) {
            $asset = new UserAccountsAsset();
            $asset->user_id = $userId;
            $asset->account_type = $accountType;
            $asset->currency_id = $currencyId;
            $asset->available = 0.000000000000000000;
            $asset->frozen = 0.000000000000000000;
            $asset->locked = 0.000000000000000000;
            $asset->borrowed_amount = 0.000000000000000000;
            $asset->interest_amount = 0.000000000000000000;
            $asset->status = 1; // 正常状态

            if (!$asset->save()) {
                throw new \RuntimeException('创建账户资产记录失败');
            }
        }

        return $asset;
    }

    /**
     * 创建资金流水记录
     */
    protected function createFlow(
        int $userId,
        int $accountId,
        int $currencyId,
        int $type,
        float|string $amount,
        float|string $before,
        float|string $after,
        int $direction,
        int $relatedId = 0
    ): void {
        $flow = new UserAccountsFlow();
        $flow->user_id = $userId;
        $flow->account_id = $accountId;
        $flow->currency_id = $currencyId;
        $flow->type = $type;
        $flow->amount = (float)$amount;
        $flow->before = (float)$before;
        $flow->after = (float)$after;
        $flow->direction = $direction;
        $flow->related_id = $relatedId;

        if (!$flow->save()) {
            throw new \RuntimeException('创建资金流水记录失败');
        }
    }
}
