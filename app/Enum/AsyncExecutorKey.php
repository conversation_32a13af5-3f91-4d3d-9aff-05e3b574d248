<?php

/**
 * AsyncExecutorKey.php
 * Author    ch<PERSON><PERSON><PERSON> (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum;

enum AsyncExecutorKey:string
{
    /**
     * 异步方法执行key
     */
    case ASYNC_EXECUTOR_QUEUE = 'async-func-executor'; //异步方法执行job进程

    case MATCH_ORDER_QUEUE = 'match-order'; //订单异步处理job进程

    case SOCKET_MESSAGE = 'socket-message'; //socket 消息处理进程key
}