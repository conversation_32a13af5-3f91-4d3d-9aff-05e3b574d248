<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单监控进程
 */

namespace App\Process\Margin;

use App\Process\BaseProcess;
use App\Model\Trade\TradeMarginConditionalOrder;
use App\Model\Trade\TradeMarginPosition;
use App\Model\Enums\Trade\Margin\ConditionalOrderStatus;
use App\Model\Enums\Trade\Margin\ExecutionType;
use App\Http\Api\Service\V1\Margin\TradeMarginService;
use App\Http\Api\Service\V1\Margin\TradeMarginConditionalService;
use App\Enum\MarginConditionalOrderRedisKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Hyperf\Logger\LoggerFactory;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

#[Process(name: 'ConditionalOrderMonitorProcess', enableCoroutine: true)]
class ConditionalOrderMonitorProcess extends BaseProcess
{
    #[Inject]
    public Redis $redis;

    #[Inject] 
    protected TradeMarginService $tradeMarginService;

    #[Inject]
    protected TradeMarginConditionalService $conditionalService;

    public \App\Logger\LoggerFactory $logger;

    /**
     * 条件订单监控日志
     */
    protected LoggerInterface $conditionalLogger;

    /**
     * 价格数据处理队列
     */
    protected Channel $priceDataChannel;

    /**
     * 队列容量
     */
    protected int $channelCapacity = 10000;

    /**
     * 清理间隔时间（秒）
     */
    protected int $cleanupInterval = 3600; // 1小时

    protected int $lastCleanupTime = 0;

    public function __construct(ContainerInterface $container, LoggerFactory $loggerFactory)
    {
        parent::__construct($container);
        $this->conditionalLogger = $loggerFactory->get('conditional_order_monitor');
        
        // 初始化Channel队列
        $this->priceDataChannel = new Channel($this->channelCapacity);
    }

    public function handle(): void
    {
        $this->conditionalLogger->info('杠杆委托订单监控进程启动');

        try {
            // 1. 启动价格数据处理协程
            $this->startPriceDataProcessor();

            // 2. 启动定期清理协程
            $this->startPeriodicCleanup();

            // 3. 启动价格数据订阅（主协程）
            $this->subscribeToPriceData();

            while (true) {
                Coroutine::sleep(1);
            }

        } catch (\Throwable $e) {
            $this->conditionalLogger->error('杠杆委托订单监控进程启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 启动价格数据处理协程
     */
    protected function startPriceDataProcessor(): void
    {
        Coroutine::create(function () {
            $this->conditionalLogger->info('价格数据处理协程启动');
            
            while (true) {
                try {
                    // 从Channel队列中获取价格数据
                    $priceData = $this->priceDataChannel->pop(0.1); // 1秒超时
                    
                    if ($priceData === false) {
                        // 超时，继续下一次循环
                        continue;
                    }
                    
                    // 处理价格数据
                    $this->processPriceData($priceData);
                    
                } catch (\Exception $e) {
                    $this->conditionalLogger->error('价格数据处理协程异常', [
                        'error' => $e->getMessage()
                    ]);
                    
                    // 异常后短暂休眠
                    Coroutine::sleep(0.1);
                }
            }
        });
    }

    /**
     * 启动定期清理协程
     */
    protected function startPeriodicCleanup(): void
    {
        Coroutine::create(function () {
            $this->conditionalLogger->info('定期清理协程启动');
            
            while (true) {
                try {
                    // 每小时清理一次过期订单
                    Coroutine::sleep(3600);
                    $this->cleanupFailedOrders();
                    
                    $this->conditionalLogger->info('定期清理失效委托订单完成');
                    
                } catch (\Exception $e) {
                    $this->conditionalLogger->error('定期清理协程异常', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
    }

    /**
     * 订阅价格数据
     */
    protected function subscribeToPriceData(): void
    {
        Coroutine::create(function () {
            $redis = redis();
            while (true) {
                try {
                    // 获取杠杆市场的成交数据订阅频道
                    $channel = TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::CRYPTO->value);

                    $this->conditionalLogger->info('开始订阅杠杆成交数据', ['channel' => $channel]);

                    // 订阅成交数据频道
                    $redis->subscribe([$channel], function ($redis, $channel, $message) {
                        $this->handlePriceMessage($channel, $message);
                    });

                } catch (\Exception $e) {
                    $this->conditionalLogger->error('订阅杠杆成交数据失败', [
                        'error' => $e->getMessage()
                    ]);
                }
                Coroutine::sleep(2);
            }
        });
    }

    /**
     * 处理价格数据消息（快速入队）
     */
    protected function handlePriceMessage(string $channel, string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !$this->validatePriceData($data)) {
                return;
            }

            $currencyId = (int)$data['currency_id'];
            $price = (string)$data['price'];
            $marketType = (int)$data['market_type'];

            // 快速将数据放入Channel队列
            $priceData = [
                'currency_id' => $currencyId,
                'price' => $price,
                'market_type' => $marketType,
                'timestamp' => microtime(true)
            ];

            // 非阻塞推送到队列
            if (!$this->priceDataChannel->push($priceData, 0.001)) {
                // 队列满了，记录警告但不阻塞
                // $this->conditionalLogger->warning('价格数据队列已满，丢弃数据', [
                //     'currency_id' => $currencyId,
                //     'price' => $price
                // ]);
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('处理价格数据消息失败', [
                'channel' => $channel,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 验证价格数据格式
     */
    protected function validatePriceData(array $data): bool
    {
        $requiredFields = ['currency_id', 'market_type', 'price', 'quantity', 'trade_time'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $this->conditionalLogger->warning('价格数据缺少必要字段', [
                    'missing_field' => $field,
                    'data' => $data
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 处理价格数据（异步处理）
     */
    protected function processPriceData(array $priceData): void
    {
        try {
            $currencyId = $priceData['currency_id'];
            $price = $priceData['price'];
            // 检查并触发符合条件的委托订单
            $this->checkAndTriggerOrdersByPrice($currencyId, $price);

        } catch (\Exception $e) {
            $this->conditionalLogger->error('处理价格数据失败', [
                'price_data' => $priceData,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 根据价格检查并触发委托订单
     */
    protected function checkAndTriggerOrdersByPrice(int $currencyId, string $currentPrice): void
    {
        $triggeredCount = 0;

        try {
            // 防止并发处理同一币种
            $lockKey = MarginConditionalOrderRedisKey::getMonitorLockKey($currencyId);
            $lockResult = $this->redis->set($lockKey, 1, ['NX', 'EX' => 10]); // 10秒锁
            
            if (!$lockResult) {
                return; // 获取锁失败，跳过这次处理
            }

            try {
                // 检查大于等于触发条件的订单 (价格上涨触发)
                $triggeredCount += $this->checkOrdersByCondition(
                    MarginConditionalOrderRedisKey::getGteKey($currencyId),
                    $currentPrice,
                    'gte'
                );
                
                // 检查小于等于触发条件的订单 (价格下跌触发)
                $triggeredCount += $this->checkOrdersByCondition(
                    MarginConditionalOrderRedisKey::getLteKey($currencyId),
                    $currentPrice,
                    'lte'
                );

                if ($triggeredCount > 0) {
                    $this->conditionalLogger->info('价格触发杠杆委托订单', [
                        'currency_id' => $currencyId,
                        'current_price' => $currentPrice,
                        'triggered_count' => $triggeredCount
                    ]);
                }

            } finally {
                // 释放锁
                $this->redis->del($lockKey);
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('检查杠杆委托订单失败', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查指定条件的订单
     */
    protected function checkOrdersByCondition(string $redisKey, string $currentPrice, string $condition): int
    {
        $triggeredCount = 0;
        
        try {
            // 根据条件获取符合触发条件的订单ID
            $orderIds = [];
            
            switch ($condition) {
                case 'gte':
                    // 获取触发价格 <= 当前价格的订单
                    $orderIds = $this->redis->zRangeByScore($redisKey, '-inf', $currentPrice);
                    break;
                    
                case 'lte':
                    // 获取触发价格 >= 当前价格的订单
                    $orderIds = $this->redis->zRangeByScore($redisKey, $currentPrice, '+inf');
                    break;
            }

            foreach ($orderIds as $orderId) {
                try {
                    // 执行委托订单
                    $success = $this->executeConditionalOrder((int)$orderId, (float)$currentPrice);
                    
                    if ($success) {
                        $triggeredCount++;
                        $this->conditionalLogger->info('杠杆委托订单触发成功', [
                            'order_id' => $orderId,
                            'current_price' => $currentPrice,
                            'redis_key' => $redisKey
                        ]);
                    }
                    
                    // 无论成功失败都从Redis中移除，避免重复处理
                    $this->redis->zRem($redisKey, $orderId);
                    $detailKey = MarginConditionalOrderRedisKey::getDetailKey((int)$orderId);
                    $this->redis->del($detailKey);
                    
                } catch (\Exception $e) {
                    $this->conditionalLogger->error('触发杠杆委托订单失败', [
                        'order_id' => $orderId,
                        'current_price' => $currentPrice,
                        'error' => $e->getMessage()
                    ]);
                    
                    // 异常情况下也要从Redis中移除，避免重复尝试
                    $this->redis->zRem($redisKey, $orderId);
                    $detailKey = MarginConditionalOrderRedisKey::getDetailKey((int)$orderId);
                    $this->redis->del($detailKey);
                }
            }
            
        } catch (\Exception $e) {
            $this->conditionalLogger->error('检查杠杆委托订单失败', [
                'redis_key' => $redisKey,
                'condition' => $condition,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }

        return $triggeredCount;
    }

    /**
     * 执行委托订单
     */
    protected function executeConditionalOrder(int $orderId, float $currentPrice): bool
    {
        try {
            return Db::transaction(function () use ($orderId, $currentPrice) {
                // 1. 获取委托订单详情
                $conditionalOrder = TradeMarginConditionalOrder::find($orderId);
                
                if (!$conditionalOrder || $conditionalOrder->getStatus() != ConditionalOrderStatus::WAITING->value) {
                    $this->conditionalLogger->warning('委托订单状态异常，跳过执行', [
                        'order_id' => $orderId,
                        'status' => $conditionalOrder?->getStatus(),
                    ]);
                    return false;
                }

                // 2. 验证关联仓位是否存在
                $position = TradeMarginPosition::where('user_id', $conditionalOrder->getUserId())
                    ->where('currency_id', $conditionalOrder->getCurrencyId())
                    ->where('margin_type', $conditionalOrder->getMarginType())
                    ->where('side', $conditionalOrder->getPositionSide())
                    ->where('status', 1)
                    ->first();
                
                if (!$position) {
                    $this->conditionalLogger->warning('关联仓位不存在或已平仓，标记委托订单为失败', [
                        'order_id' => $orderId,
                        'currency_id' => $conditionalOrder->getCurrencyId(),
                        'user_id' => $conditionalOrder->getUserId(),
                        'margin_type' => $conditionalOrder->getMarginType(),
                        'position_side' => $conditionalOrder->getPositionSide(),
                    ]);
                    
                    // 标记委托订单为失败
                    $conditionalOrder->setStatus(ConditionalOrderStatus::FAILED->value);
                    $conditionalOrder->setFailureReason('关联仓位不存在或已平仓');
                    $conditionalOrder->save();
                    return false;
                }

                // 3. 根据执行类型创建平仓订单
                $executionType = ExecutionType::from($conditionalOrder->getExecutionType());
                $orderParams = [
                    'user_id' => $conditionalOrder->getUserId(),
                    'currency_id' => $conditionalOrder->getCurrencyId(),
                    'side' => $this->getClosePositionSide($conditionalOrder->getPositionSide()),
                    'margin_type' => $conditionalOrder->getMarginType(),
                    'leverage' => $position->getLeverage(),
                    'quantity' => $conditionalOrder->getCloseQuantity(),
                    'price' => $executionType === ExecutionType::LIMIT ? $conditionalOrder->getExecutionPrice() : null,
                    'order_type' => $executionType === ExecutionType::MARKET ? 'market' : 'limit',
                ];

                try {
                    $marginOrder = $this->tradeMarginService->createMarginOrder($orderParams);
                    
                    // 更新委托订单状态为已触发
                    $conditionalOrder->setStatus(ConditionalOrderStatus::TRIGGERED->value);
                    $conditionalOrder->setTriggerTime(new \DateTime());
                    $conditionalOrder->setTriggeredOrderId($marginOrder->getId());
                    $conditionalOrder->save();

                    $this->conditionalLogger->info('委托订单触发成功', [
                        'conditional_order_id' => $orderId,
                        'triggered_margin_order_id' => $marginOrder->getId(),
                        'trigger_price' => $conditionalOrder->getTriggerPrice(),
                        'current_price' => $currentPrice,
                        'close_quantity' => $conditionalOrder->getCloseQuantity(),
                    ]);

                    return true;

                } catch (\Exception $e) {
                    // 平仓失败，标记委托订单为失败
                    $conditionalOrder->setStatus(ConditionalOrderStatus::FAILED->value);
                    $conditionalOrder->setFailureReason('平仓执行失败: ' . $e->getMessage());
                    $conditionalOrder->save();

                    $this->conditionalLogger->error('委托订单平仓执行失败', [
                        'conditional_order_id' => $orderId,
                        'error' => $e->getMessage(),
                        'trigger_price' => $conditionalOrder->getTriggerPrice(),
                        'current_price' => $currentPrice,
                    ]);

                    return false;
                }
            });

        } catch (\Throwable $e) {
            $this->conditionalLogger->error('执行委托订单异常', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 根据仓位方向获取平仓方向
     */
    protected function getClosePositionSide(int $positionSide): int
    {
        // 做多仓位平仓 = 卖出 (2)
        // 做空仓位平仓 = 买入 (1)
        return $positionSide === 1 ? 2 : 1;
    }

    /**
     * 清理失效的委托订单
     */
    protected function cleanupFailedOrders(): void
    {
        try {
            // 清理数据库中失败的委托订单记录（可选）
            $cleanedCount = TradeMarginConditionalOrder::where('status', ConditionalOrderStatus::FAILED->value)
                ->where('created_at', '<', \Carbon\Carbon::now()->subDays(7)) // 7天前的失败订单
                ->delete();

            if ($cleanedCount > 0) {
                $this->conditionalLogger->info('清理失效杠杆委托订单', ['count' => $cleanedCount]);
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('清理失效杠杆委托订单失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARGIN_MONITOR_ENABLE', true);
    }
} 