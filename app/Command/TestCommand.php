<?php

/**
 * TestCommand.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Command;

use App\Enum\Config\TradeConfigKey;
use App\Enum\Config\UserVipLevelKey;
use App\Http\Api\Event\UserCertificationEvent;
use App\MarketData\Service\CryptoCurrencySync;
use App\MarketData\Service\MatchEngine\DepthSaveService;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Trade\TradeConfig;
use App\Model\User\UserAccountsAsset;
use App\Model\User\VipLevel;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;

#[Command]
class TestCommand extends HyperfCommand
{

    #[Inject]
    protected Redis $redis;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('test');
    }
    public function handle()
    {
//        $depth = ApplicationContext::getContainer()->get(DepthSaveService::class)
//            ->getDepth("BTCUSDT",1,"asks");
//        var_dump($depth);
        //初始化用户的资产数据
//        ApplicationContext::getContainer()->get(EventDispatcherInterface::class)->dispatch(
//            new UserCertificationEvent(2)
//        );

        $accountService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
//        $accountService->addAvailableAsset(
//            2,
//            AccountType::MARGIN->value,
//            866,
//            0.01,
//            FlowsType::RECHARGE->value
//        );

        $accountService->addAvailableAsset(
            2,
            AccountType::ISOLATED->value,
            866,
            1000,
            FlowsType::RECHARGE->value,
            assetField: UserAccountsAsset::FIELD_MARGIN_QUOTE
        );

//        $levelData = VipLevel::query()->where('status',1)->get()->toArray();
//
//        foreach ($levelData as $level){
//            $key = UserVipLevelKey::getConfigKey($level['id']);
//            $this->redis->hMset($key,$level);
//        }

//        $currencyConfig = TradeConfig::query()->with(['currency:id,status'])->whereHas('currency',function ($query){
//            $query->where('status',1);
//        })->get()->toArray();
//        foreach ($currencyConfig as $config){
//            $key = TradeConfigKey::getTradeConfigKey($config['currency_id'],$config['market_type']);
//            unset($config['currency']);
//            $this->redis->hMset($key,$config);
//        }

    }
}